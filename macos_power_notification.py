from PySide6 import QtWidgets, Qt<PERSON>ore, QtGui
import sys
import os
import json
import math
from power_monitor_service import PowerMonitorService


class MacOSPowerNotification(QtWidgets.QWidget):
    """macOS-style power notification with premium animations and exact design match."""
    
    def __init__(self):
        super().__init__()
        
        # Load macOS-style configuration
        self.config = self.load_macos_config()
        
        # Initialize power monitor
        self.power_monitor = PowerMonitorService(1000)
        self.power_monitor.power_connected.connect(self.trigger_notification)
        
        # Animation system
        self.animation_controller = MacOSAnimationController(self)
        
        # State management
        self.is_visible = False
        self.is_animating = False
        self.current_battery_percent = self.get_initial_battery_percent()
        self.current_charging_state = "charging"
        
        # Timers
        self.dismiss_timer = QtCore.QTimer(self)
        self.dismiss_timer.setSingleShot(True)
        self.dismiss_timer.timeout.connect(self.hide_notification)
        
        self.content_update_timer = QtCore.QTimer(self)
        self.content_update_timer.timeout.connect(self.update_dynamic_content)
        
        # Initialize UI
        self.init_macos_ui()
        
    def load_macos_config(self):
        """Load macOS-style configuration."""
        config_path = os.path.join(os.path.dirname(__file__), "macos_style_config.json")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading macOS config: {e}")
            return self.get_fallback_config()
            
    def get_fallback_config(self):
        """Fallback configuration."""
        return {
            "design_system": {
                "dimensions": {"width": 280, "height": 44, "border_radius": 22},
                "macos_style": {"background": {"base": "rgba(28, 28, 30, 0.98)"}},
                "battery_indicator": {"fill_colors": {"charging": "#30D158"}}
            },
            "behavior": {"display_duration": 4000, "position": {"y": 20}}
        }

    def get_initial_battery_percent(self):
        """Get the actual battery percentage at startup."""
        try:
            battery_info = self.power_monitor.get_current_battery_info()
            if battery_info:
                return battery_info['percent']
        except:
            pass
        return 85  # Fallback if battery info not available
        
    def init_macos_ui(self):
        """Initialize macOS-style user interface."""
        # Advanced window setup
        self.setWindowFlags(
            QtCore.Qt.FramelessWindowHint |
            QtCore.Qt.Tool |
            QtCore.Qt.WindowStaysOnTopHint |
            QtCore.Qt.NoDropShadowWindowHint
        )
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.setAttribute(QtCore.Qt.WA_NoSystemBackground)
        
        # Set dimensions
        dimensions = self.config['design_system']['dimensions']
        self.setFixedSize(dimensions['width'], dimensions['height'])
        
        # Create layout
        self.main_layout = QtWidgets.QHBoxLayout(self)
        padding = dimensions.get('inner_padding', 16)
        self.main_layout.setContentsMargins(padding, 0, padding, 0)
        self.main_layout.setSpacing(dimensions.get('spacing', 12))
        
        # Charging text
        self.charging_label = QtWidgets.QLabel("Charging")
        self.charging_label.setObjectName("charging_text")
        self.main_layout.addWidget(self.charging_label)
        
        # Add stretch to push percentage and battery to the right
        self.main_layout.addStretch()
        
        # Percentage label - show actual battery percentage
        self.percentage_label = QtWidgets.QLabel(f"{self.current_battery_percent}%")
        self.percentage_label.setObjectName("percentage_text")
        self.main_layout.addWidget(self.percentage_label)
        
        # Battery indicator
        self.battery_widget = MacOSBatteryIndicator(self)
        self.battery_widget.battery_level = self.current_battery_percent  # Set initial level
        self.main_layout.addWidget(self.battery_widget)
        
        # Apply styling
        self.apply_macos_styling()
        
        # Position off-screen initially
        self.position_widget(hidden=True)
        self.hide()
        
    def apply_macos_styling(self):
        """Apply macOS-style typography."""
        typography = self.config['design_system']['typography']
        
        charging_style = f"""
        QLabel#charging_text {{
            color: {typography['charging_text']['color']};
            font-family: {typography['charging_text']['font_family']};
            font-size: {typography['charging_text']['font_size']}px;
            font-weight: {typography['charging_text']['font_weight']};
            letter-spacing: {typography['charging_text']['letter_spacing']}px;
            background: transparent;
            border: none;
        }}
        """
        
        percentage_style = f"""
        QLabel#percentage_text {{
            color: {typography['percentage_text']['color']};
            font-family: {typography['percentage_text']['font_family']};
            font-size: {typography['percentage_text']['font_size']}px;
            font-weight: {typography['percentage_text']['font_weight']};
            letter-spacing: {typography['percentage_text']['letter_spacing']}px;
            background: transparent;
            border: none;
        }}
        """
        
        self.setStyleSheet(charging_style + percentage_style)
        
    def paintEvent(self, event):
        """Custom paint event for macOS-style background."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        painter.setRenderHint(QtGui.QPainter.SmoothPixmapTransform)
        
        # Draw macOS-style background
        self.draw_macos_background(painter)
        
        super().paintEvent(event)
        
    def draw_macos_background(self, painter):
        """Draw the exact macOS-style background."""
        rect = self.rect()
        border_radius = self.config['design_system']['dimensions']['border_radius']
        
        # Background
        bg_color = QtGui.QColor(self.config['design_system']['macos_style']['background']['base'])
        painter.setBrush(QtGui.QBrush(bg_color))
        
        # Border
        border_config = self.config['design_system']['macos_style']['border']
        border_color = QtGui.QColor(border_config['color'])
        pen = QtGui.QPen(border_color, border_config['width'])
        painter.setPen(pen)
        
        # Draw rounded rectangle
        painter.drawRoundedRect(rect.adjusted(1, 1, -1, -1), border_radius, border_radius)
        
        # Apply premium shadow effect
        self.apply_premium_shadows()
        
    def apply_premium_shadows(self):
        """Apply premium multi-layer shadow system."""
        # Primary shadow
        shadow = QtWidgets.QGraphicsDropShadowEffect()
        shadow_config = self.config['design_system']['macos_style']['shadows'][0]
        shadow.setBlurRadius(shadow_config['blur'])
        shadow.setColor(QtGui.QColor(shadow_config['color']))
        shadow.setOffset(shadow_config['offset'][0], shadow_config['offset'][1])
        self.setGraphicsEffect(shadow)
        
    def trigger_notification(self):
        """Trigger macOS-style notification."""
        if self.is_visible or self.is_animating:
            return

        # Update content with current battery info
        self.update_notification_content()

        # Show with premium animation
        self.animation_controller.start_entrance_sequence()
        
    def update_notification_content(self):
        """Update notification content based on battery state."""
        battery_info = self.power_monitor.get_current_battery_info()
        
        if battery_info:
            self.current_battery_percent = battery_info['percent']
            
            # Determine charging state
            if battery_info['percent'] >= 95:
                self.current_charging_state = "battery_full"
                charging_text = "Charged"
            elif battery_info.get('charging_speed') == "Fast":
                self.current_charging_state = "fast_charging"
                charging_text = "Fast Charging"
            else:
                self.current_charging_state = "charging"
                charging_text = "Charging"
        else:
            # Demo values
            charging_text = "Charging"
            
        # Update labels
        self.charging_label.setText(charging_text)
        self.percentage_label.setText(f"{self.current_battery_percent}%")
        
        # Update battery indicator
        self.battery_widget.update_battery_level(self.current_battery_percent)
        
    def update_dynamic_content(self):
        """Update dynamic content while visible."""
        if self.is_visible:
            # Only update if battery info has actually changed
            battery_info = self.power_monitor.get_current_battery_info()
            if battery_info and battery_info['percent'] != self.current_battery_percent:
                self.update_notification_content()
            
    def position_widget(self, hidden=False):
        """Position widget with perfect centering."""
        screen = QtWidgets.QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        
        x = screen_geometry.x() + (screen_geometry.width() - self.width()) // 2
        
        if hidden:
            y = screen_geometry.y() - self.height() - 50
        else:
            y = screen_geometry.y() + self.config['behavior']['position']['y']
            
        self.move(x, y)
        
    def get_perfect_position_rect(self):
        """Get perfect centered position rectangle."""
        screen = QtWidgets.QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        x = screen_geometry.x() + (screen_geometry.width() - self.width()) // 2
        y = screen_geometry.y() + self.config['behavior']['position']['y']
        return QtCore.QRect(x, y, self.width(), self.height())
        
    def show_notification(self):
        """Show notification with macOS-style entrance."""
        self.is_animating = True
        self.position_widget(hidden=True)
        self.show()
        self.setWindowOpacity(0.0)
        
        # Start content updates
        self.content_update_timer.start(1000)
        
        # Trigger entrance animation
        self.animation_controller.start_entrance_sequence()
        
    def hide_notification(self):
        """Hide notification with macOS-style exit."""
        if not self.is_visible or self.is_animating:
            return
            
        self.is_animating = True
        self.dismiss_timer.stop()
        self.content_update_timer.stop()
        
        # Trigger exit animation
        self.animation_controller.start_exit_sequence()
        
    def on_entrance_complete(self):
        """Called when entrance animation completes."""
        self.is_animating = False
        self.is_visible = True
        
        # Ensure perfect positioning
        perfect_rect = self.get_perfect_position_rect()
        self.setGeometry(perfect_rect)
        
        # Start auto-dismiss timer
        self.dismiss_timer.start(self.config['behavior']['display_duration'])
        
    def on_exit_complete(self):
        """Called when exit animation completes."""
        self.hide()
        self.is_animating = False
        self.is_visible = False
        
    def mousePressEvent(self, event):
        """Handle click with premium feedback."""
        if event.button() == QtCore.Qt.LeftButton:
            self.animation_controller.trigger_click_feedback()
            QtCore.QTimer.singleShot(120, self.hide_notification)
            event.accept()
            
    def enterEvent(self, event):
        """Handle hover enter."""
        if self.is_visible and not self.is_animating:
            self.animation_controller.trigger_hover_enter()
        event.accept()
        
    def leaveEvent(self, event):
        """Handle hover exit."""
        if self.is_visible and not self.is_animating:
            self.animation_controller.trigger_hover_exit()
        event.accept()
        
    def start_monitoring(self):
        """Start power monitoring."""
        self.power_monitor.start_monitoring()
        
    def stop_monitoring(self):
        """Stop power monitoring."""
        self.power_monitor.stop_monitoring()
        
    def trigger_test_notification(self):
        """Trigger test notification."""
        self.trigger_notification()
        
    def closeEvent(self, event):
        """Handle close event."""
        self.stop_monitoring()
        self.content_update_timer.stop()
        self.dismiss_timer.stop()
        self.animation_controller.cleanup()
        event.accept()


class MacOSBatteryIndicator(QtWidgets.QWidget):
    """macOS-style battery indicator with animated fill."""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent_widget = parent
        self.battery_level = parent.current_battery_percent  # Use actual battery level
        self._animated_level = 0

        # Get battery config
        self.battery_config = parent.config['design_system']['battery_indicator']

        # Set size
        self.setFixedSize(
            self.battery_config['width'] + 4,  # +4 for terminal
            self.battery_config['height']
        )

        # Animation for battery fill
        self.fill_animation = QtCore.QPropertyAnimation(self, b"animated_level")
        self.fill_animation.setDuration(800)
        self.fill_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)

    def get_animated_level(self):
        """Get current animated level."""
        return self._animated_level

    def set_animated_level(self, level):
        """Set animated level and trigger repaint."""
        self._animated_level = level
        self.update()

    # Property for animation
    animated_level = QtCore.Property(float, get_animated_level, set_animated_level)

    def update_battery_level(self, level):
        """Update battery level with animation."""
        self.battery_level = level

        # Animate fill
        self.fill_animation.setStartValue(self._animated_level)
        self.fill_animation.setEndValue(level)
        self.fill_animation.start()

    def paintEvent(self, event):
        """Paint the macOS-style battery indicator."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # Battery body
        body_rect = QtCore.QRect(0, 0, self.battery_config['width'], self.battery_config['height'])
        border_radius = self.battery_config['border_radius']

        # Draw battery outline
        border_color = QtGui.QColor(self.battery_config['border_color'])
        painter.setPen(QtGui.QPen(border_color, self.battery_config['border_width']))
        painter.setBrush(QtCore.Qt.NoBrush)
        painter.drawRoundedRect(body_rect, border_radius, border_radius)

        # Draw battery terminal (the little bump on the right)
        terminal_config = self.battery_config['terminal']
        terminal_rect = QtCore.QRect(
            self.battery_config['width'] + 1,
            (self.battery_config['height'] - terminal_config['height']) // 2,
            terminal_config['width'],
            terminal_config['height']
        )
        terminal_color = QtGui.QColor(terminal_config['color'])
        painter.setBrush(QtGui.QBrush(terminal_color))
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawRoundedRect(terminal_rect, terminal_config['border_radius'], terminal_config['border_radius'])

        # Draw battery fill
        if self._animated_level > 0:
            fill_width = (self.battery_config['width'] - 4) * (self._animated_level / 100)
            fill_rect = QtCore.QRect(2, 2, int(fill_width), self.battery_config['height'] - 4)

            # Get fill color based on level
            fill_color = self.get_fill_color()
            painter.setBrush(QtGui.QBrush(fill_color))
            painter.setPen(QtCore.Qt.NoPen)
            painter.drawRoundedRect(fill_rect, border_radius - 1, border_radius - 1)

            # Add subtle glow effect
            self.draw_battery_glow(painter, fill_rect, fill_color)

    def get_fill_color(self):
        """Get fill color based on battery level."""
        colors = self.battery_config['fill_colors']

        if self.battery_level >= 80:
            return QtGui.QColor(colors['high'])
        elif self.battery_level >= 50:
            return QtGui.QColor(colors['medium'])
        elif self.battery_level >= 20:
            return QtGui.QColor(colors['low'])
        else:
            return QtGui.QColor(colors['critical'])

    def draw_battery_glow(self, painter, fill_rect, fill_color):
        """Draw subtle glow around battery fill."""
        glow_color = QtGui.QColor(fill_color)
        glow_color.setAlpha(60)

        # Create glow effect
        glow_rect = fill_rect.adjusted(-1, -1, 1, 1)
        painter.setBrush(QtGui.QBrush(glow_color))
        painter.drawRoundedRect(glow_rect, 2, 2)


class MacOSAnimationController:
    """Premium animation controller for macOS-style sequences."""

    def __init__(self, widget):
        self.widget = widget
        self.config = widget.config['premium_animations']
        self.active_animations = []

    def start_entrance_sequence(self):
        """Start the premium macOS-style entrance sequence."""
        self.cleanup()

        # Phase 1: Slide down with scale
        self.create_slide_animation()

        # Phase 2: Content reveal (delayed)
        QtCore.QTimer.singleShot(300, self.create_content_reveal)

        # Phase 3: Start micro-animations
        QtCore.QTimer.singleShot(600, self.start_micro_animations)

    def create_slide_animation(self):
        """Create the main slide down animation."""
        # Geometry animation
        geometry_anim = QtCore.QPropertyAnimation(self.widget, b"geometry")
        geometry_anim.setDuration(600)
        geometry_anim.setEasingCurve(QtCore.QEasingCurve.OutCubic)

        # Calculate positions
        start_rect = self.widget.geometry()
        end_rect = self.widget.get_perfect_position_rect()

        geometry_anim.setStartValue(start_rect)
        geometry_anim.setEndValue(end_rect)

        # Opacity animation
        opacity_anim = QtCore.QPropertyAnimation(self.widget, b"windowOpacity")
        opacity_anim.setDuration(500)
        opacity_anim.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        opacity_anim.setStartValue(0.0)
        opacity_anim.setEndValue(1.0)

        # Start animations
        geometry_anim.start()
        opacity_anim.start()

        self.active_animations.extend([geometry_anim, opacity_anim])

        # Connect completion
        opacity_anim.finished.connect(self.widget.on_entrance_complete)

    def create_content_reveal(self):
        """Create content reveal animation."""
        # Animate battery fill
        if hasattr(self.widget, 'battery_widget'):
            self.widget.battery_widget.update_battery_level(self.widget.current_battery_percent)

    def start_micro_animations(self):
        """Start subtle micro-animations."""
        # Subtle pulse animation for the whole widget
        pulse_anim = QtCore.QPropertyAnimation(self.widget, b"windowOpacity")
        pulse_anim.setDuration(3000)
        pulse_anim.setEasingCurve(QtCore.QEasingCurve.InOutSine)
        pulse_anim.setLoopCount(-1)
        pulse_anim.setStartValue(1.0)
        pulse_anim.setKeyValueAt(0.5, 0.95)
        pulse_anim.setEndValue(1.0)
        pulse_anim.start()

        self.active_animations.append(pulse_anim)

    def start_exit_sequence(self):
        """Start the exit animation sequence."""
        self.cleanup()

        # Scale and fade out
        geometry_anim = QtCore.QPropertyAnimation(self.widget, b"geometry")
        geometry_anim.setDuration(400)
        geometry_anim.setEasingCurve(QtCore.QEasingCurve.InCubic)

        current_rect = self.widget.geometry()
        exit_rect = QtCore.QRect(
            current_rect.x() + 8, current_rect.y() - 20,
            current_rect.width() - 16, current_rect.height() - 8
        )

        geometry_anim.setStartValue(current_rect)
        geometry_anim.setEndValue(exit_rect)

        # Fade out
        fade_anim = QtCore.QPropertyAnimation(self.widget, b"windowOpacity")
        fade_anim.setDuration(400)
        fade_anim.setEasingCurve(QtCore.QEasingCurve.InCubic)
        fade_anim.setStartValue(self.widget.windowOpacity())
        fade_anim.setEndValue(0.0)

        geometry_anim.start()
        fade_anim.start()

        self.active_animations.extend([geometry_anim, fade_anim])

        # Connect completion
        fade_anim.finished.connect(self.widget.on_exit_complete)

    def trigger_click_feedback(self):
        """Trigger click feedback."""
        click_anim = QtCore.QPropertyAnimation(self.widget, b"geometry")
        click_anim.setDuration(120)
        click_anim.setEasingCurve(QtCore.QEasingCurve.OutCubic)

        current_rect = self.widget.geometry()
        click_rect = QtCore.QRect(
            current_rect.x() + 1, current_rect.y(),
            current_rect.width() - 2, current_rect.height() - 1
        )

        click_anim.setStartValue(current_rect)
        click_anim.setKeyValueAt(0.5, click_rect)
        click_anim.setEndValue(self.widget.get_perfect_position_rect())
        click_anim.start()

    def trigger_hover_enter(self):
        """Trigger hover enter effect."""
        hover_anim = QtCore.QPropertyAnimation(self.widget, b"geometry")
        hover_anim.setDuration(200)
        hover_anim.setEasingCurve(QtCore.QEasingCurve.OutCubic)

        current_rect = self.widget.geometry()
        hover_rect = QtCore.QRect(
            current_rect.x() - 1, current_rect.y(),
            current_rect.width() + 2, current_rect.height() + 1
        )

        hover_anim.setStartValue(current_rect)
        hover_anim.setEndValue(hover_rect)
        hover_anim.start()

    def trigger_hover_exit(self):
        """Trigger hover exit effect."""
        hover_anim = QtCore.QPropertyAnimation(self.widget, b"geometry")
        hover_anim.setDuration(300)
        hover_anim.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        hover_anim.setStartValue(self.widget.geometry())
        hover_anim.setEndValue(self.widget.get_perfect_position_rect())
        hover_anim.start()

    def cleanup(self):
        """Clean up active animations."""
        for anim in self.active_animations:
            if anim.state() == QtCore.QAbstractAnimation.Running:
                anim.stop()
        self.active_animations.clear()
