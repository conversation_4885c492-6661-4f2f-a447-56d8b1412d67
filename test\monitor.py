import sys
import psutil
import os
from PySide6 import QtWidgets, Qt<PERSON>ore, QtGui, QtSvgWidgets # Changed QtSvg to QtSvgWidgets

# Make sure to install psutil:
# pip install psutil

# Paths to SVG icon files (place these in an "images" folder in the same directory as this script):
ICON_DIR = os.path.dirname(__file__)
IMAGES_SUBDIR = os.path.join(ICON_DIR, "images")

# Ensure the "images" subdirectory path is correct
BATTERY_BOLT_ICON    = os.path.join(IMAGES_SUBDIR, "battery-bolt.svg")
BATTERY_EMPTY_ICON   = os.path.join(IMAGES_SUBDIR, "battery-empty.svg")
BATTERY_LOW_ICON     = os.path.join(IMAGES_SUBDIR, "battery-low.svg")
BATTERY_MID_ICON     = os.path.join(IMAGES_SUBDIR, "battery-mid.svg")
BATTERY_FULL_ICON    = os.path.join(IMAGES_SUBDIR, "battery-full.svg")
CPU_ICON             = os.path.join(IMAGES_SUBDIR, "cpu-bolt.svg")
RAM_ICON             = os.path.join(IMAGES_SUBDIR, "ram-memory.svg")


class ResourceWidget(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        # Update every 2 seconds
        self.timer = QtCore.QTimer(self, timeout=self.update_stats)
        self.timer.start(2000)

    def init_ui(self):
        # Rounded, semi-transparent background
        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint | QtCore.Qt.WindowType.Tool)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.resize(350, 180)

        # Main container with rounded corners and light background
        container = QtWidgets.QFrame(self)
        container.setStyleSheet("""
            QFrame {
                background-color: rgba(250, 250, 250, 230);
                border-radius: 12px;
            }
        """)
        container.setGeometry(0, 0, 350, 180) # Ensure container takes full widget size

        layout = QtWidgets.QVBoxLayout(container)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # Battery row
        self.battery_icon = QtSvgWidgets.QSvgWidget() # Corrected module
        self.battery_icon.setFixedSize(24, 24)
        self.battery_bar = QtWidgets.QProgressBar()
        self.battery_bar.setTextVisible(True)
        self.battery_bar.setRange(0, 100)
        self.battery_bar.setFixedHeight(16)
        self.battery_bar.setStyleSheet("""
            QProgressBar {
                background-color: #E0E0E0;
                border-radius: 8px;
                text-align: center;
                color: #000;
            }
            QProgressBar::chunk {
                background-color: #3B82F6;
                border-radius: 8px;
            }
        """)
        battery_row = QtWidgets.QHBoxLayout()
        battery_row.addWidget(self.battery_icon)
        battery_row.addSpacing(8)
        battery_row.addWidget(self.battery_bar)
        layout.addLayout(battery_row)

        # CPU row
        self.cpu_icon = QtSvgWidgets.QSvgWidget(CPU_ICON) # Corrected module
        self.cpu_icon.setFixedSize(24, 24)
        self.cpu_bar = QtWidgets.QProgressBar()
        self.cpu_bar.setTextVisible(True)
        self.cpu_bar.setRange(0, 100)
        self.cpu_bar.setFixedHeight(16)
        self.cpu_bar.setStyleSheet("""
            QProgressBar {
                background-color: #E0E0E0;
                border-radius: 8px;
                text-align: center;
                color: #000;
            }
            QProgressBar::chunk {
                background-color: #F97316;
                border-radius: 8px;
            }
        """)
        cpu_row = QtWidgets.QHBoxLayout()
        cpu_row.addWidget(self.cpu_icon)
        cpu_row.addSpacing(8)
        cpu_row.addWidget(self.cpu_bar)
        layout.addLayout(cpu_row)

        # RAM row
        self.ram_icon = QtSvgWidgets.QSvgWidget(RAM_ICON) # Corrected module
        self.ram_icon.setFixedSize(24, 24)
        self.ram_bar = QtWidgets.QProgressBar()
        self.ram_bar.setTextVisible(True)
        self.ram_bar.setRange(0, 100)
        self.ram_bar.setFixedHeight(16)
        self.ram_bar.setStyleSheet("""
            QProgressBar {
                background-color: #E0E0E0;
                border-radius: 8px;
                text-align: center;
                color: #000;
            }
            QProgressBar::chunk {
                background-color: #10B981;
                border-radius: 8px;
            }
        """)
        ram_row = QtWidgets.QHBoxLayout()
        ram_row.addWidget(self.ram_icon)
        ram_row.addSpacing(8)
        ram_row.addWidget(self.ram_bar)
        layout.addLayout(ram_row)

        # Context menu on right-click
        self.setContextMenuPolicy(QtCore.Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.open_menu)

        self.update_stats()
        self.show()

    def update_stats(self):
        # Battery status
        batt = psutil.sensors_battery()
        if batt:
            percent = int(batt.percent)
            plugged = batt.power_plugged
            self.battery_bar.setValue(percent)
            self.battery_bar.setFormat(f"{percent}%")

            if plugged:
                # Charging: show bolt icon and green bar
                if os.path.exists(BATTERY_BOLT_ICON):
                    self.battery_icon.load(BATTERY_BOLT_ICON)
                else:
                    print(f"Warning: Icon not found at {BATTERY_BOLT_ICON}")
                self.battery_bar.setStyleSheet("""
                    QProgressBar {
                        background-color: #E0E0E0;
                        border-radius: 8px;
                        text-align: center;
                        color: #000;
                    }
                    QProgressBar::chunk {
                        background-color: #10B981; /* green */
                        border-radius: 8px;
                    }
                """)
            else:
                # Discharging: choose icon by level and blue bar
                icon_to_load = None
                if percent < 10:
                    icon_to_load = BATTERY_EMPTY_ICON
                elif percent < 40:
                    icon_to_load = BATTERY_LOW_ICON
                elif percent < 80:
                    icon_to_load = BATTERY_MID_ICON
                else: # percent >= 80
                    icon_to_load = BATTERY_FULL_ICON
                
                if icon_to_load and os.path.exists(icon_to_load):
                    self.battery_icon.load(icon_to_load)
                else:
                    # Fallback or print warning if a specific icon wasn't found
                    print(f"Warning: Battery icon not found at {icon_to_load}. Using fallback or no icon.")
                    if os.path.exists(BATTERY_EMPTY_ICON): # Default to empty if preferred not found
                         self.battery_icon.load(BATTERY_EMPTY_ICON)
                    else:
                         print(f"Warning: Fallback battery icon not found at {BATTERY_EMPTY_ICON}")


                self.battery_bar.setStyleSheet("""
                    QProgressBar {
                        background-color: #E0E0E0;
                        border-radius: 8px;
                        text-align: center;
                        color: #000;
                    }
                    QProgressBar::chunk {
                        background-color: #3B82F6; /* blue */
                        border-radius: 8px;
                    }
                """)
        else:
            # If no battery found (desktop), show “N/A” and empty icon
            self.battery_bar.setValue(0)
            self.battery_bar.setFormat("N/A")
            if os.path.exists(BATTERY_EMPTY_ICON):
                self.battery_icon.load(BATTERY_EMPTY_ICON)
            else:
                print(f"Warning: Icon not found at {BATTERY_EMPTY_ICON}")

        # CPU usage
        cpu = int(psutil.cpu_percent())
        self.cpu_bar.setValue(cpu)
        self.cpu_bar.setFormat(f"{cpu}%")
        if not os.path.exists(CPU_ICON):
             print(f"Warning: Icon not found at {CPU_ICON}")
        elif not self.cpu_icon.renderer().isValid(): # Check if SVG loaded correctly
             print(f"Warning: CPU SVG icon at {CPU_ICON} might be invalid or not loaded.")


        # RAM usage
        mem = psutil.virtual_memory()
        ram = int(mem.percent)
        self.ram_bar.setValue(ram)
        self.ram_bar.setFormat(f"{ram}%")
        if not os.path.exists(RAM_ICON):
            print(f"Warning: Icon not found at {RAM_ICON}")
        elif not self.ram_icon.renderer().isValid(): # Check if SVG loaded correctly
            print(f"Warning: RAM SVG icon at {RAM_ICON} might be invalid or not loaded.")


    def open_menu(self, pos):
        menu = QtWidgets.QMenu(self)
        exit_action = menu.addAction("Exit")
        action = exit_action.triggered.connect(QtWidgets.QApplication.instance().quit)
        menu.exec(self.mapToGlobal(pos))


if __name__ == "__main__":
    # Create "images" directory if it doesn't exist.
    # This is a good place for users to be reminded to put their icons.
    if not os.path.exists(IMAGES_SUBDIR):
        try:
            os.makedirs(IMAGES_SUBDIR)
            print(f"Created directory: {IMAGES_SUBDIR}")
            print(f"Please make sure your SVG icon files ({os.path.basename(BATTERY_BOLT_ICON)}, etc.) are in this directory.")
        except OSError as e:
            print(f"Error creating directory {IMAGES_SUBDIR}: {e}. Please create it manually and place icons there.")
            # sys.exit(1) # Optionally exit if icon directory is critical

    app = QtWidgets.QApplication(sys.argv)
    # Check for psutil availability early
    try:
        import psutil
    except ImportError:
        print("Error: psutil module not found. Please install it by running 'pip install psutil'")
        # You could show a QMessageBox here if the app still runs partially
        # msg_box = QtWidgets.QMessageBox()
        # msg_box.setIcon(QtWidgets.QMessageBox.Icon.Critical)
        # msg_box.setText("psutil module not found. Please install it by running 'pip install psutil'")
        # msg_box.setWindowTitle("Dependency Error")
        # msg_box.exec()
        sys.exit(1) # Exit if psutil is essential for the app to start

    widget = ResourceWidget()
    sys.exit(app.exec())