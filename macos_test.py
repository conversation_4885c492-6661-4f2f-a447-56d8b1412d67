#!/usr/bin/env python3
"""
macOS-Style Power Notification Test
Experience the exact design from your image with premium animations.
"""

from PySide6 import QtWidgets, QtCore
import sys
from macos_power_notification import MacOSPowerNotification


def main():
    """Test the macOS-style notification."""
    app = QtWidgets.QApplication(sys.argv)
    
    # Create macOS-style notification
    notification = MacOSPowerNotification()
    
    print("🍎 macOS-Style Power Notification Test")
    print("✨ Exact design match with your image")
    print("🎬 Premium animations and micro-interactions")
    print("🔋 Animated battery indicator with fill")
    print("🎨 Perfect macOS styling and typography")
    print("\n⚡ Showing notification now...")
    
    # Show the macOS-style notification
    notification.show_notification()
    
    # Auto-exit after 8 seconds to see full animation cycle
    QtCore.QTimer.singleShot(8000, app.quit)
    
    # Run app
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
