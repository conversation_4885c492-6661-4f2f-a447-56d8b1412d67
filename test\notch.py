import sys
import os
# Add this block here:
# --- Start of addition ---
# You need to replace 'PATH_TO_YOUR_PY_SIDE_6_PLUGINS' below
# with the actual path to the 'plugins' directory inside your PySide6 installation.
# Example for Windows: r'C:\Python\Python39\Lib\site-packages\PySide6\plugins'
# Example for macOS/Linux: '/path/to/your/venv/lib/python3.9/site-packages/PySide6/plugins'
# Use raw string (r'') on Windows paths to avoid issues with backslashes.
pyside6_plugin_path = r'C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PySide6\plugins' # <-- REPLACE THIS STRING
if os.path.exists(pyside6_plugin_path):
    os.environ['QT_PLUGIN_PATH'] = pyside6_plugin_path
    print(f"DEBUG: Set QT_PLUGIN_PATH to: {pyside6_plugin_path}")
else:
    print(f"ERROR: PySide6 plugins directory not found at: {pyside6_plugin_path}")
    print("Please update the 'pyside6_plugin_path' variable in the script with the correct path.")
# --- End of addition ---
from PySide6 import QtWidgets, QtCore, QtGui
import subprocess

def resource_path(relative_path):
    try: base_path = sys._MEIPASS
    except AttributeError: base_path = os.path.abspath(".")
    # Ensure the 'images' directory is correctly prefixed by the base_path
    if relative_path.startswith("images/"):
        return os.path.join(base_path, relative_path)
    return os.path.join(base_path, relative_path)


class DraggableFileListItem(QtWidgets.QListWidgetItem):
    def __init__(self, file_path, source_list_widget, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.source_list_widget = source_list_widget
        self.setText(os.path.basename(file_path))
        icon_provider = QtWidgets.QFileIconProvider()
        item_icon = icon_provider.icon(QtCore.QFileInfo(file_path))
        # if item_icon.isNull():
            # print(f"DEBUG: FileIconProvider returned null icon for {file_path}") # Less verbose
        self.setIcon(item_icon)


class ClipboardTextListItem(QtWidgets.QListWidgetItem):
    MAX_DISPLAY_LEN = 70 

    def __init__(self, text_content, parent_list_widget=None):
        super().__init__(parent_list_widget)
        self.text_content = text_content
        self.source_list_widget = parent_list_widget
        
        display_text = text_content
        if len(text_content) > self.MAX_DISPLAY_LEN:
            display_text = text_content[:self.MAX_DISPLAY_LEN-3] + "..."
        self.setText(display_text.replace("\n", " ").replace("\r", "")) 
        
        icon_path = resource_path("images/text-doc-light.svg") 
        # print(f"DEBUG: ClipboardTextListItem - Attempting icon from: {icon_path}")
        # print(f"DEBUG: ClipboardTextListItem - File exists? {os.path.exists(icon_path)}")
        text_doc_icon = QtGui.QIcon(icon_path)
        
        use_fallback = True
        if not text_doc_icon.isNull():
            # print(f"DEBUG: ClipboardTextListItem - text_doc_icon loaded. Actual name: '{text_doc_icon.name()}'")
            pixmap = text_doc_icon.pixmap(QtCore.QSize(16, 16))
            if not pixmap.isNull():
                # print(f"DEBUG: ClipboardTextListItem - text_doc_icon pixmap generated. Setting icon.")
                self.setIcon(text_doc_icon)
                use_fallback = False
            # else:
                # print(f"ERROR: ClipboardTextListItem - text_doc_icon pixmap(16,16) IS NULL.")
        # else:
            # if os.path.exists(icon_path):
                # print(f"ERROR: ClipboardTextListItem - text_doc_icon IS NULL from existing file '{icon_path}'. SVG Plugin issue?")
            # else:
                # print(f"ERROR: ClipboardTextListItem - text_doc_icon file not found '{icon_path}'.")

        if use_fallback:
            # print(f"DEBUG: ClipboardTextListItem - Using fallback system icon.")
            style = QtWidgets.QApplication.style()
            fallback_icon = style.standardIcon(QtWidgets.QStyle.StandardPixmap.SP_FileIcon)
            # if fallback_icon.isNull():
                 # print(f"ERROR: ClipboardTextListItem - Fallback SP_FileIcon is also null.")
            self.setIcon(fallback_icon)
        self.setToolTip(text_content)

class FilePillWidget(QtWidgets.QWidget):
    INITIAL_PILL_WIDTH = 200 
    INITIAL_PILL_HEIGHT = 30 
    EXPANDED_WIDTH = 380     
    EXPANDED_HEIGHT = 280    
    ANIMATION_DURATION = 220 
    CORNER_RADIUS = 10       
    PILL_CORNER_RADIUS_FACTOR = 0.5 
    MAX_CLIPBOARD_HISTORY = 50

    def __init__(self):
        super().__init__()
        font_families = ["SF Pro Text", "Helvetica Neue", "Arial", "sans-serif"] 
        app_font = QtGui.QFont()
        for family in font_families:
            temp_font = QtGui.QFont(family)
            if QtGui.QFontInfo(temp_font).family() == family: 
                app_font.setFamily(family)
                break
        app_font.setPointSize(8.5) 
        self.setFont(app_font)

        self.staged_files_paths = []
        self.clipboard_history_data = []  
        self.is_dragging_widget = False
        self.drag_offset = QtCore.QPoint()
        self._is_expanded = False
        self._animation_in_progress = False
        self.svg_icon_path = resource_path("images/dropzone-icon-light.svg") 

        self.setWindowFlags(QtCore.Qt.FramelessWindowHint | QtCore.Qt.Tool | QtCore.Qt.WindowStaysOnTopHint)
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.setAcceptDrops(True)

        self._main_bg_color = QtGui.QColor(28, 28, 30, 238) 
        self._text_color_primary = QtGui.QColor(220, 220, 225, 230) 
        self._text_color_secondary = QtGui.QColor(160, 160, 165, 200) 

        self._setup_ui_elements() 
        self._apply_stylesheets() 
        self._setup_animations()
        
        self._recenter_widget(animate=False) 
        self._update_widget_display_state()

        self._leave_timer = QtCore.QTimer(self); self._leave_timer.setSingleShot(True)
        self._leave_timer.setInterval(450); self._leave_timer.timeout.connect(self._attempt_collapse)

        self.clipboard = QtWidgets.QApplication.clipboard()
        self.clipboard.dataChanged.connect(self._on_clipboard_data_changed)
        self._last_clipboard_urls = []
        self._last_clipboard_text = None 
        self.show()

    def _setup_ui_elements(self):
        self.outer_layout = QtWidgets.QVBoxLayout(self)
        self.outer_layout.setContentsMargins(0,0,0,0)

        self.instruction_label = QtWidgets.QLabel(self) 
        self.instruction_label.setAlignment(QtCore.Qt.AlignCenter)
        self.instruction_label.setVisible(True) 

        self.tab_widget = QtWidgets.QTabWidget(self)
        self.tab_widget.setVisible(False) 
        self.tab_widget.setIconSize(QtCore.QSize(14,14)) 

        self.stage_tab_widget = QtWidgets.QWidget()
        self.stage_layout = QtWidgets.QVBoxLayout(self.stage_tab_widget)
        self.stage_layout.setContentsMargins(8, 6, 8, 8) 
        self.file_stage_list_widget = QtWidgets.QListWidget()
        self._configure_list_widget(self.file_stage_list_widget)
        self.stage_layout.addWidget(self.file_stage_list_widget)
        
        stage_icon_path = resource_path("images/stage-tab-light.svg") 
        print(f"DEBUG: Attempting to load stage icon from: {stage_icon_path}")
        print(f"DEBUG: Stage icon file exists? {os.path.exists(stage_icon_path)}")
        stage_icon = QtGui.QIcon(stage_icon_path)

        if stage_icon.isNull():
            print(f"ERROR: Stage icon IS NULL after QIcon('{stage_icon_path}').")
            if not os.path.exists(stage_icon_path): print(f"  -> File {stage_icon_path} does NOT exist.")
            else: print(f"  -> File {stage_icon_path} EXISTS. SVG plugin issue or invalid SVG content is highly likely.")
            self.tab_widget.addTab(self.stage_tab_widget, "Stage") 
        else:
            print(f"DEBUG: Stage icon loaded (not null). Actual name: '{stage_icon.name()}'")
            pixmap = stage_icon.pixmap(QtCore.QSize(16, 16)) 
            if pixmap.isNull():
                print(f"ERROR: Stage icon pixmap(16,16) IS NULL. Cannot render SVG.")
                self.tab_widget.addTab(self.stage_tab_widget, "Stage") 
            else:
                print(f"DEBUG: Stage icon pixmap(16,16) generated, size: {pixmap.size().width()}x{pixmap.size().height()}. Adding to tab.")
                self.tab_widget.addTab(self.stage_tab_widget, stage_icon, "")

        self.clipboard_tab_widget = QtWidgets.QWidget()
        self.clipboard_layout = QtWidgets.QVBoxLayout(self.clipboard_tab_widget)
        self.clipboard_layout.setContentsMargins(8, 6, 8, 8)
        self.clipboard_history_list_widget = QtWidgets.QListWidget()
        self._configure_list_widget(self.clipboard_history_list_widget)
        self.clipboard_layout.addWidget(self.clipboard_history_list_widget)
        
        clipboard_icon_path = resource_path("images/clipboard-tab-light.svg") 
        print(f"DEBUG: Attempting to load clipboard icon from: {clipboard_icon_path}")
        print(f"DEBUG: Clipboard icon file exists? {os.path.exists(clipboard_icon_path)}")
        clipboard_icon = QtGui.QIcon(clipboard_icon_path)

        if clipboard_icon.isNull():
            print(f"ERROR: Clipboard icon IS NULL after QIcon('{clipboard_icon_path}').")
            if not os.path.exists(clipboard_icon_path): print(f"  -> File {clipboard_icon_path} does NOT exist.")
            else: print(f"  -> File {clipboard_icon_path} EXISTS. SVG plugin issue or invalid SVG content is highly likely.")
            self.tab_widget.addTab(self.clipboard_tab_widget, "Clip") 
        else:
            print(f"DEBUG: Clipboard icon loaded (not null). Actual name: '{clipboard_icon.name()}'")
            pixmap = clipboard_icon.pixmap(QtCore.QSize(16,16))
            if pixmap.isNull():
                print(f"ERROR: Clipboard icon pixmap(16,16) IS NULL. Cannot render SVG.")
                self.tab_widget.addTab(self.clipboard_tab_widget, "Clip")
            else:
                print(f"DEBUG: Clipboard icon pixmap(16,16) generated, size: {pixmap.size().width()}x{pixmap.size().height()}. Adding to tab.")
                self.tab_widget.addTab(self.clipboard_tab_widget, clipboard_icon, "") 

        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        self.outer_layout.addWidget(self.tab_widget)
        self.instruction_label.raise_() 

        self._rich_instruction_label = QtWidgets.QLabel(self)
        self._rich_instruction_label.setObjectName("RichInstructionLabel") 
        self._rich_instruction_label.setAlignment(QtCore.Qt.AlignCenter)
        self._rich_instruction_label.setVisible(False)

    # ... (rest of the FilePillWidget class methods - _apply_stylesheets, etc. remain the same)
    # ... (Assume all other methods of FilePillWidget are unchanged from the previous "full code" response)


    def _apply_stylesheets(self):
        # macOS-inspired stylesheet
        stylesheet = f"""
            FilePillWidget {{ 
                /* Font set in __init__ */
            }}

            QTabWidget::pane {{
                border: none; 
                background-color: transparent;
                padding: 0px; 
            }}

            QTabBar {{
                alignment: center; 
                padding-top: 4px; 
                padding-bottom: 2px; 
            }}

            QTabBar::tab {{
                background-color: transparent; 
                color: {self._text_color_secondary.name(QtGui.QColor.HexArgb)};
                border: 1px solid transparent; 
                border-radius: 6px; 
                min-width: 30px;  
                height: 22px;    
                padding: 2px 8px 2px 8px;  
                margin-left: 3px; 
                margin-right: 3px;
                font-size: 8.5pt; 
                font-weight: 400; 
            }}

            QTabBar::tab:selected {{
                background-color: rgba(70, 70, 75, 180); 
                color: {self._text_color_primary.name(QtGui.QColor.HexArgb)};
                font-weight: 500; 
            }}
            QTabBar::tab:!selected:hover {{
                background-color: rgba(55, 55, 60, 150);
                color: {self._text_color_primary.name(QtGui.QColor.HexArgb)};
            }}

            QListWidget {{
                background-color: transparent; 
                border: none;
                padding: 2px; 
                outline: 0px; 
            }}
            QListWidget::item {{
                background-color: transparent; 
                color: {self._text_color_primary.name(QtGui.QColor.HexArgb)}; 
                padding: 7px 8px; 
                border-radius: 5px; 
                margin-bottom: 1px; 
            }}
            QListWidget::item:hover {{
                background-color: rgba(65, 65, 70, 120); 
            }}
            QListWidget::item:selected {{
                background-color: rgba(80, 85, 95, 160); 
                color: white; 
            }}
            
            QLabel#RichInstructionLabel {{ 
                 background-color: transparent;
                 color: {self._text_color_secondary.name(QtGui.QColor.HexArgb)};
                 font-size: 9pt; 
            }}
        """
        self.setStyleSheet(stylesheet)
        self.instruction_label.setStyleSheet(
            f"background-color: transparent; color: {self._text_color_secondary.name(QtGui.QColor.HexArgb)}; font-size: 8pt; font-weight:400;"
        )

    def _configure_list_widget(self, list_widget):
        list_widget.setDragEnabled(True)
        list_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        list_widget.setItemDelegate(QtWidgets.QStyledItemDelegate(self))
        list_widget.viewport().setAcceptDrops(False)
        list_widget.setDropIndicatorShown(False)
        list_widget.startDrag = lambda actions, lw=list_widget: self._custom_list_item_start_drag(actions, lw)
        list_widget.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        list_widget.customContextMenuRequested.connect(lambda pos, lw=list_widget: self._show_list_context_menu(pos, lw))
        list_widget.setIconSize(QtCore.QSize(18,18)) 
        list_widget.setSpacing(0) 


    def _custom_list_item_start_drag(self, supportedActions, source_list_widget):
        selected_items = source_list_widget.selectedItems()
        if not selected_items: return
        mime_data = QtCore.QMimeData(); drag = QtGui.QDrag(self)
        first_item = selected_items[0]
        preview_text = ""; icon_pixmap = QtGui.QPixmap(20,20) 

        if isinstance(first_item, DraggableFileListItem):
            urls = [QtCore.QUrl.fromLocalFile(item.file_path) for item in selected_items if isinstance(item, DraggableFileListItem)]
            if not urls: return
            mime_data.setUrls(urls); preview_text = os.path.basename(first_item.file_path)
            if len(selected_items) > 1: preview_text += f" (+{len(selected_items)-1})"
            provider = QtWidgets.QFileIconProvider()
            icon_pixmap = provider.icon(QtCore.QFileInfo(first_item.file_path)).pixmap(20,20)
        elif isinstance(first_item, ClipboardTextListItem):
            mime_data.setText(first_item.text_content); preview_text = first_item.text_content
            if len(preview_text) > 40: preview_text = preview_text[:37] + "..."
            if len(selected_items) > 1: preview_text += f" (+{len(selected_items)-1})"
            icon_path = resource_path("images/text-doc-light.svg")
            if os.path.exists(icon_path): icon_pixmap = QtGui.QPixmap(icon_path).scaled(20,20, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
            else: icon_pixmap.fill(QtCore.Qt.transparent) 
        else: return 

        fm = self.fontMetrics()
        icon_w = 0 if icon_pixmap.isNull() else icon_pixmap.width()
        dw = fm.horizontalAdvance(preview_text) + icon_w + 18 
        dh = max( (0 if icon_pixmap.isNull() else icon_pixmap.height()) + 6, fm.height() + 6)
        
        drag_pixmap = QtGui.QPixmap(dw, dh); drag_pixmap.fill(QtCore.Qt.transparent)
        p = QtGui.QPainter(drag_pixmap); p.setRenderHint(QtGui.QPainter.Antialiasing)
        p.setBrush(QtGui.QColor(50, 50, 55, 210)); p.setPen(QtCore.Qt.NoPen)
        p.drawRoundedRect(drag_pixmap.rect(), 5, 5)
        if not icon_pixmap.isNull(): p.drawPixmap(3, (dh - icon_pixmap.height()) // 2, icon_pixmap)
        p.setPen(self._text_color_primary)
        p.drawText(QtCore.QRect(icon_w+6, 0, dw-(icon_w+9), dh), QtCore.Qt.AlignVCenter|QtCore.Qt.AlignLeft, preview_text)
        p.end()
        
        drag.setMimeData(mime_data); drag.setPixmap(drag_pixmap)
        drag.setHotSpot(QtCore.QPoint(10, dh//2)); drag.exec(QtCore.Qt.CopyAction)


    def _setup_animations(self):
        self.geometry_animation = QtCore.QPropertyAnimation(self, b"geometry", self)
        self.geometry_animation.setDuration(self.ANIMATION_DURATION)
        self.geometry_animation.setEasingCurve(QtCore.QEasingCurve.InOutSine) 
        self.geometry_animation.finished.connect(self._on_animation_finished)

    def _on_animation_finished(self):
        self._animation_in_progress = False
        if not self._is_expanded: 
            primary_screen = QtGui.QGuiApplication.primaryScreen()
            screen_geometry = primary_screen.availableGeometry() if primary_screen else None
            if screen_geometry:
                target_x = screen_geometry.x() + (screen_geometry.width() - self.INITIAL_PILL_WIDTH) // 2
                standard_y = screen_geometry.y() + 10 
                expected_pill_geo = QtCore.QRect(target_x, standard_y, self.INITIAL_PILL_WIDTH, self.INITIAL_PILL_HEIGHT)
                if self.geometry() != expected_pill_geo:
                    self.setGeometry(expected_pill_geo)
        self._update_widget_display_state() 

    def _animate_geometry(self, target_geo):
        if self.geometry_animation.state()==QtCore.QAbstractAnimation.Running and self.geometry_animation.endValue()==target_geo: return
        self._animation_in_progress = True; current_geo = self.geometry()
        if not self.isVisible() or (current_geo.width()==0 and current_geo.height()==0): 
            self.setGeometry(target_geo); self._on_animation_finished(); return
        self.geometry_animation.setStartValue(current_geo); self.geometry_animation.setEndValue(target_geo); self.geometry_animation.start()

    def _animate_to_expanded(self):
        if self._is_expanded or self._animation_in_progress: return
        self._is_expanded = True 
        current_y = self.y()
        primary_screen = QtGui.QGuiApplication.primaryScreen();
        if not primary_screen: return
        screen_geometry = primary_screen.availableGeometry()
        target_x = screen_geometry.x() + (screen_geometry.width() - self.EXPANDED_WIDTH) // 2
        self._update_widget_display_state() 
        self._animate_geometry(QtCore.QRect(target_x, current_y, self.EXPANDED_WIDTH, self.EXPANDED_HEIGHT))
        
    def _animate_to_collapsed(self):
        if not self._is_expanded or self._animation_in_progress: return
        self._is_expanded = False 
        current_y = self.y()
        primary_screen = QtGui.QGuiApplication.primaryScreen();
        if not primary_screen: return
        screen_geometry = primary_screen.availableGeometry()
        target_x = screen_geometry.x() + (screen_geometry.width() - self.INITIAL_PILL_WIDTH) // 2
        self._update_widget_display_state() 
        self._animate_geometry(QtCore.QRect(target_x, current_y, self.INITIAL_PILL_WIDTH, self.INITIAL_PILL_HEIGHT))


    def _on_tab_changed(self, index):
        self._update_widget_display_state()

    def _update_widget_display_state(self):
        has_staged_files = bool(self.staged_files_paths)
        has_clipboard_items = bool(self.clipboard_history_data)
        
        self.file_stage_list_widget.setVisible(False)
        self.clipboard_history_list_widget.setVisible(False)
        self._rich_instruction_label.setVisible(False)

        if self._is_expanded:
            self.instruction_label.setVisible(False) 
            self.tab_widget.setVisible(True)
            self.tab_widget.raise_()
            self._rich_instruction_label.raise_() 

            current_tab_idx = self.tab_widget.currentIndex()
            current_tab_empty_message = ""
            icon_uri = resource_path("images/dropzone-icon-light.svg") 
            if not os.path.exists(icon_uri): icon_uri = ""
            
            text_color = self._text_color_secondary.name(QtGui.QColor.HexArgb)
            text_color_primary = self._text_color_primary.name(QtGui.QColor.HexArgb)

            if current_tab_idx == 0: 
                if has_staged_files: self.file_stage_list_widget.setVisible(True)
                else: 
                    current_tab_empty_message = f"""<div style='text-align:center; padding-top:20%;'>
                                                      <img src='{icon_uri}' width='32' height='32' style='margin-bottom:8px;'/><br/>
                                                      <span style='font-size:9.5pt; color:{text_color_primary}; font-weight:500;'>Drop Files to Stage</span><br/>
                                                      <span style='font-size:7.5pt; color:{text_color};'>Drag items out from here.</span></div>"""
            elif current_tab_idx == 1: 
                if has_clipboard_items: self.clipboard_history_list_widget.setVisible(True)
                else:
                    current_tab_empty_message = f"""<div style='text-align:center; padding-top:25%;'>
                                                     <span style='font-size:9.5pt; color:{text_color_primary}; font-weight:500;'>Clipboard Empty</span><br/>
                                                     <span style='font-size:7.5pt; color:{text_color};'>Copied files & text will appear.<br/>Max: {FilePillWidget.MAX_CLIPBOARD_HISTORY}</span></div>"""
            
            if current_tab_empty_message:
                self._rich_instruction_label.setTextFormat(QtCore.Qt.RichText)
                self._rich_instruction_label.setText(current_tab_empty_message)
                self._rich_instruction_label.setVisible(True)
            
            tab_bar_geo = self.tab_widget.tabBar().geometry()
            content_y_offset = tab_bar_geo.bottom() + 2 
            content_height = self.height() - content_y_offset - 5 
            content_width = self.width() - 10 

            if self._rich_instruction_label.isVisible():
                 self._rich_instruction_label.setGeometry(5, content_y_offset, content_width, content_height)

        else: 
            self.tab_widget.setVisible(False)
            self._rich_instruction_label.setVisible(False)
            self.instruction_label.setVisible(True)
            self.instruction_label.raise_()

            if has_staged_files:
                collapsed_text = f"{len(self.staged_files_paths)} Item{'s' if len(self.staged_files_paths) > 1 else ''}" 
            else:
                collapsed_text = "Drop Zone" 
            
            if self.instruction_label.text() != collapsed_text:
                self.instruction_label.setText(collapsed_text)
            self.instruction_label.setGeometry(self.rect())

        self.update() 

    def _attempt_collapse(self):
        if self._is_expanded and not self.underMouse() and not self._is_child_widget_active():
            active_list = None
            if self.tab_widget.isVisible(): 
                current_idx = self.tab_widget.currentIndex()
                if current_idx == 0: active_list = self.file_stage_list_widget
                elif current_idx == 1: active_list = self.clipboard_history_list_widget
            
            if active_list and active_list.isVisible() and active_list.rect().contains(active_list.mapFromGlobal(QtGui.QCursor.pos())):
                self._leave_timer.start(1000); return 
            self._animate_to_collapsed()

    def _is_child_widget_active(self):
        active_popup = QtWidgets.QApplication.activePopupWidget()
        if active_popup and self.isAncestorOf(active_popup):
            if active_popup.rect().contains(active_popup.mapFromGlobal(QtGui.QCursor.pos())):
                return True
        
        if not self.tab_widget.isVisible(): return False 

        global_mouse_pos = QtGui.QCursor.pos()
        if self.tab_widget.tabBar().rect().contains(self.tab_widget.tabBar().mapFromGlobal(global_mouse_pos)): return True
        
        current_tab_content_widget = self.tab_widget.currentWidget()
        if current_tab_content_widget and current_tab_content_widget.rect().contains(current_tab_content_widget.mapFromGlobal(global_mouse_pos)):
            active_list = None 
            if self.tab_widget.currentIndex() == 0 and self.file_stage_list_widget.isVisible(): active_list = self.file_stage_list_widget
            elif self.tab_widget.currentIndex() == 1 and self.clipboard_history_list_widget.isVisible(): active_list = self.clipboard_history_list_widget
            if active_list and active_list.viewport().rect().contains(active_list.viewport().mapFromGlobal(global_mouse_pos)): 
                return True
        return False

    def enterEvent(self, event: QtCore.QEvent):
        self._leave_timer.stop()
        if not self._is_expanded and not self._animation_in_progress:
            self._animate_to_expanded()
        event.accept()

    def leaveEvent(self, event: QtCore.QEvent):
        if self._is_expanded and not self._animation_in_progress and not self._is_child_widget_active():
            self._leave_timer.start()
        event.accept()

    def paintEvent(self, event: QtGui.QPaintEvent):
        painter = QtGui.QPainter(self); painter.setRenderHint(QtGui.QPainter.Antialiasing)
        painter.setBrush(self._main_bg_color); painter.setPen(QtCore.Qt.NoPen)
        current_rectF = QtCore.QRectF(self.rect()) 
        
        radius = 0.0
        if not self._is_expanded:
            radius = min(current_rectF.width(), current_rectF.height()) * self.PILL_CORNER_RADIUS_FACTOR
        else:
            radius = float(self.CORNER_RADIUS) 
            
        path = QtGui.QPainterPath()
        path.addRoundedRect(current_rectF, radius, radius)
        painter.drawPath(path)

    def dragEnterEvent(self, event: QtGui.QDragEnterEvent):
        if event.mimeData().hasUrls():
             event.acceptProposedAction()
        else: event.ignore()

    def dropEvent(self, event: QtGui.QDropEvent):
        if event.mimeData().hasUrls():
            new_files_count = 0
            files_to_add = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if file_path not in self.staged_files_paths:
                        files_to_add.append(file_path)
            
            if files_to_add:
                for file_path in files_to_add:
                    self.staged_files_paths.append(file_path)
                    self.file_stage_list_widget.addItem(DraggableFileListItem(file_path, self.file_stage_list_widget))
                
                if not self._is_expanded:
                    self.tab_widget.setCurrentIndex(0) 
                    self._animate_to_expanded() 
                else:
                    if self.tab_widget.currentIndex() != 0:
                        self.tab_widget.setCurrentIndex(0) 
                    else:
                        self._update_widget_display_state() 

            event.acceptProposedAction()
        else: event.ignore()
    
    def _on_clipboard_data_changed(self):
        mime_data = self.clipboard.mimeData()
        if not mime_data: return
        new_items_added = False
        if mime_data.hasUrls():
            current_urls_paths = [url.toLocalFile() for url in mime_data.urls() if url.isLocalFile()]
            if current_urls_paths: 
                if sorted(current_urls_paths) == sorted(self._last_clipboard_urls) and \
                   len(current_urls_paths) == len(self._last_clipboard_urls): return 
                self._last_clipboard_urls = list(current_urls_paths)
                self._last_clipboard_text = None 
                for file_path in reversed(current_urls_paths): 
                    self.clipboard_history_data = [item for item in self.clipboard_history_data if not (item['type'] == 'file' and item['content'] == file_path)]
                    self.clipboard_history_data.insert(0, {'type': 'file', 'content': file_path, 'display_name': os.path.basename(file_path)})
                    new_items_added = True
        elif mime_data.hasText(): 
            text_content = mime_data.text()
            if not text_content.strip(): return
            if text_content == self._last_clipboard_text: return 
            self._last_clipboard_text = text_content
            self._last_clipboard_urls = [] 
            self.clipboard_history_data = [item for item in self.clipboard_history_data if not (item['type'] == 'text' and item['content'] == text_content)]
            self.clipboard_history_data.insert(0, {'type': 'text', 'content': text_content, 'display_name': text_content})
            new_items_added = True

        if new_items_added:
            self.clipboard_history_data = self.clipboard_history_data[:FilePillWidget.MAX_CLIPBOARD_HISTORY]
            self.clipboard_history_list_widget.clear()
            for item_data in self.clipboard_history_data:
                list_item = None
                if item_data['type'] == 'file': list_item = DraggableFileListItem(item_data['content'], self.clipboard_history_list_widget)
                elif item_data['type'] == 'text': list_item = ClipboardTextListItem(item_data['content'], self.clipboard_history_list_widget)
                if list_item:
                    list_item.setData(QtCore.Qt.UserRole, item_data['content']) 
                    list_item.setData(QtCore.Qt.UserRole + 1, item_data['type']) 
                    self.clipboard_history_list_widget.addItem(list_item)
            self._update_widget_display_state() 

    def mousePressEvent(self, event: QtGui.QMouseEvent):
        mouse_event_pos = event.position().toPoint() 
        can_drag_widget = not self._is_expanded or \
                          (event.modifiers() & QtCore.Qt.AltModifier) or \
                          (self._is_expanded and mouse_event_pos.y() < self.INITIAL_PILL_HEIGHT) 
        
        if event.button() == QtCore.Qt.LeftButton and can_drag_widget:
            if self._is_expanded and self.tab_widget.isVisible(): 
                tab_bar = self.tab_widget.tabBar()
                tab_bar_pos = tab_bar.mapFromParent(mouse_event_pos)
                if tab_bar.rect().contains(tab_bar_pos) and tab_bar.tabAt(tab_bar_pos) != -1 : 
                    super().mousePressEvent(event); return 

                active_list = None
                current_tab_index = self.tab_widget.currentIndex()
                if current_tab_index == 0 and self.file_stage_list_widget.isVisible(): active_list = self.file_stage_list_widget
                elif current_tab_index == 1 and self.clipboard_history_list_widget.isVisible(): active_list = self.clipboard_history_list_widget
                
                if active_list and active_list.geometry().contains(active_list.mapFromParent(mouse_event_pos)) and \
                   active_list.itemAt(active_list.mapFromParent(mouse_event_pos)):
                    super().mousePressEvent(event); return 
            
            self.is_dragging_widget = True; self.drag_offset = event.globalPosition().toPoint() - self.frameGeometry().topLeft(); event.accept()
        else: super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QtGui.QMouseEvent):
        if self.is_dragging_widget and event.buttons()==QtCore.Qt.LeftButton: self.move(event.globalPosition().toPoint()-self.drag_offset); event.accept()
        else: super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QtGui.QMouseEvent):
        if event.button()==QtCore.Qt.LeftButton and self.is_dragging_widget: self.is_dragging_widget = False; event.accept()
        else: super().mouseReleaseEvent(event)
        
    def contextMenuEvent(self, event: QtGui.QContextMenuEvent):
        widget_under_mouse = QtWidgets.QApplication.widgetAt(event.globalPos())
        is_over_list_viewport = False
        if widget_under_mouse: 
            if widget_under_mouse == self.file_stage_list_widget.viewport() or \
            (hasattr(self, 'clipboard_history_list_widget') and widget_under_mouse == self.clipboard_history_list_widget.viewport()):
                is_over_list_viewport = True
        
        if is_over_list_viewport or \
            widget_under_mouse == self.file_stage_list_widget or \
            (hasattr(self, 'clipboard_history_list_widget') and widget_under_mouse == self.clipboard_history_list_widget):
            return 

        menu = QtWidgets.QMenu(self); self._style_context_menu(menu)
        self._populate_widget_context_menu(menu)
        if menu.actions(): menu.exec(event.globalPos())
        
    def _show_list_context_menu(self, position, list_widget_source):
        menu = QtWidgets.QMenu(self); self._style_context_menu(menu)
        item = list_widget_source.itemAt(position)
        if item: 
            list_widget_source.setCurrentItem(item) 
            folder_icon = QtGui.QIcon.fromTheme("document-open-folder", QtGui.QIcon(resource_path("images/folder-light.svg")))
            copy_icon = QtGui.QIcon.fromTheme("edit-copy", QtGui.QIcon(resource_path("images/copy-light.svg")))
            delete_icon = QtGui.QIcon.fromTheme("edit-delete", QtGui.QIcon(resource_path("images/delete-light.svg")))
            add_to_stage_icon = QtGui.QIcon.fromTheme("go-up", QtGui.QIcon(resource_path("images/add-to-stage-light.svg"))) 
            
            if isinstance(item, DraggableFileListItem):
                menu.addAction(folder_icon, "Show in Folder").triggered.connect(lambda chk=False, pth=item.file_path: self._show_file_in_folder(pth))
                menu.addAction(copy_icon, "Copy Path").triggered.connect(lambda chk=False, pth=item.file_path: self.clipboard.setText(pth))
                menu.addSeparator()
                if list_widget_source == self.file_stage_list_widget:
                    menu.addAction(delete_icon, "Remove from Stage").triggered.connect(self._remove_selected_staged_files)
                elif list_widget_source == self.clipboard_history_list_widget:
                    menu.addAction(delete_icon, "Remove from History").triggered.connect(self._remove_selected_clipboard_history_files)
                    menu.addAction(add_to_stage_icon, "Add to Stage").triggered.connect(lambda chk=False, itm=item: self._add_history_item_to_stage(itm))
            elif isinstance(item, ClipboardTextListItem):
                menu.addAction(copy_icon, "Copy Text").triggered.connect(lambda chk=False, txt=item.text_content: self.clipboard.setText(txt))
                menu.addSeparator()
                if list_widget_source == self.clipboard_history_list_widget:
                    menu.addAction(delete_icon, "Remove from History").triggered.connect(self._remove_selected_clipboard_history_files)
        
        if not menu.actions() or not item : 
             self._populate_widget_context_menu(menu, add_separator_if_needed=(bool(menu.actions())))
        elif item and menu.actions() : 
            if not menu.actions()[-1].isSeparator(): menu.addSeparator()
            self._populate_widget_context_menu(menu, add_separator_if_needed=False)

        if menu.actions(): menu.exec(list_widget_source.viewport().mapToGlobal(position))

    def _style_context_menu(self, menu):
         menu.setStyleSheet(f"""
            QMenu {{
                background-color: {self._main_bg_color.lighter(115).name(QtGui.QColor.HexArgb)}; 
                border: 1px solid rgba(70,70,75,150);
                border-radius: 6px;
                color: {self._text_color_primary.name(QtGui.QColor.HexArgb)};
                padding: 5px;
                font-size: 8.5pt; 
            }}
            QMenu::item {{
                padding: 6px 20px 6px 15px; 
                background-color: transparent;
                border-radius: 4px; 
            }}
            QMenu::item:selected {{
                background-color: rgba(80,85,95,180); 
                color: white;
            }}
            QMenu::separator {{
                height: 1px;
                background: rgba(80,80,85,100); 
                margin: 4px 2px; 
            }}
            QMenu::icon {{
                padding-left: 5px;
                width: 14px; 
                height: 14px;
            }}
        """)
    
    def _populate_widget_context_menu(self, menu, add_separator_if_needed=True):
        if add_separator_if_needed and menu.actions() and not menu.actions()[-1].isSeparator():
            menu.addSeparator()

        actions_added_here = False
        clear_icon = QtGui.QIcon.fromTheme("edit-clear", QtGui.QIcon(resource_path("images/clear-all-light.svg"))) 

        if self.staged_files_paths:
            menu.addAction(clear_icon,"Clear Stage").triggered.connect(self._clear_all_staged_files)
            actions_added_here = True
        if self.clipboard_history_data: 
            if actions_added_here: menu.addSeparator() 
            menu.addAction(clear_icon,"Clear Clipboard").triggered.connect(self._clear_all_clipboard_history)
            actions_added_here = True
        
        if actions_added_here: menu.addSeparator()
            
        menu.addAction("Recenter").triggered.connect(lambda: self._recenter_widget(True))
        if menu.actions() and not menu.actions()[-1].isSeparator() : menu.addSeparator()
        menu.addAction(QtGui.QIcon.fromTheme("application-exit"), "Quit").triggered.connect(QtWidgets.QApplication.instance().quit)


    def _remove_selected_staged_files(self):
        selected = self.file_stage_list_widget.selectedItems()
        if not selected: return
        for item in selected:
            if isinstance(item,DraggableFileListItem) and item.file_path in self.staged_files_paths: self.staged_files_paths.remove(item.file_path)
            self.file_stage_list_widget.takeItem(self.file_stage_list_widget.row(item))
        self._update_widget_display_state()

    def _clear_all_staged_files(self):
        self.file_stage_list_widget.clear(); self.staged_files_paths.clear()
        self._update_widget_display_state()

    def _remove_selected_clipboard_history_files(self):
        selected_items = self.clipboard_history_list_widget.selectedItems()
        if not selected_items: return
        content_to_remove_specs = []
        for item_widget in selected_items:
            original_content = item_widget.data(QtCore.Qt.UserRole)
            item_type = item_widget.data(QtCore.Qt.UserRole + 1)
            content_to_remove_specs.append({'type': item_type, 'content': original_content})
        for item_widget in reversed(selected_items): 
            self.clipboard_history_list_widget.takeItem(self.clipboard_history_list_widget.row(item_widget))
        new_history_data = []
        temp_specs_copy = list(content_to_remove_specs) 
        for data_item in self.clipboard_history_data:
            is_match = False
            for spec_to_match in temp_specs_copy:
                if data_item['type'] == spec_to_match['type'] and data_item['content'] == spec_to_match['content']:
                    is_match = True; temp_specs_copy.remove(spec_to_match); break
            if not is_match: new_history_data.append(data_item)
        self.clipboard_history_data = new_history_data
        self._update_widget_display_state()

    def _clear_all_clipboard_history(self):
        self.clipboard_history_list_widget.clear(); self.clipboard_history_data.clear() 
        self._last_clipboard_urls = []; self._last_clipboard_text = None   
        self._update_widget_display_state()

    def _add_history_item_to_stage(self, history_item_to_stage):
        if isinstance(history_item_to_stage, DraggableFileListItem): 
            file_path = history_item_to_stage.file_path
            if file_path not in self.staged_files_paths:
                self.staged_files_paths.append(file_path)
                self.file_stage_list_widget.addItem(DraggableFileListItem(file_path, self.file_stage_list_widget))
            self.tab_widget.setCurrentIndex(0) 

    def _show_file_in_folder(self, file_path):
        info = QtCore.QFileInfo(file_path); norm_fp = os.path.normpath(info.absoluteFilePath())
        if sys.platform=="win32": subprocess.run(['explorer','/select,',norm_fp])
        elif sys.platform=="darwin": subprocess.run(["open","-R",norm_fp])
        else: 
            dir_path = info.absolutePath()
            if not QtGui.QDesktopServices.openUrl(QtCore.QUrl.fromLocalFile(dir_path)):
                 print(f"Could not open directory: {dir_path}") 

    def _recenter_widget(self, animate=True):
        primary_screen = QtGui.QGuiApplication.primaryScreen();
        if not primary_screen: return 
        s_geom = primary_screen.availableGeometry()
        target_w = self.EXPANDED_WIDTH if self._is_expanded else self.INITIAL_PILL_WIDTH
        target_h = self.EXPANDED_HEIGHT if self._is_expanded else self.INITIAL_PILL_HEIGHT
        tx,ty = s_geom.x()+(s_geom.width()-target_w)//2, s_geom.y() + 10 
        target_geo = QtCore.QRect(tx, ty, target_w, target_h)
        if animate:
            if self.geometry() != target_geo : self._animate_geometry(target_geo)
        else: 
            self.setGeometry(target_geo)
            if not self._animation_in_progress: self._on_animation_finished()

if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    
    print("DEBUG: Checking for supported image formats by QImageReader...")
    supported_formats = QtGui.QImageReader.supportedImageFormats()
    has_svg_support = False
    for fmt_qbytearray in supported_formats:
        fmt_str = bytes(fmt_qbytearray).decode().lower()
        # print(f"DEBUG: Found format: {fmt_str}")
        if fmt_str == "svg":
            has_svg_support = True
    if has_svg_support:
        print("DEBUG: SVG format IS listed as supported by QImageReader.")
    else:
        print("ERROR: SVG format NOT listed in supportedImageFormats by QImageReader!")
        print("       This strongly indicates the Qt SVG plugin (e.g., qsvg.dll/dylib/so) is missing or not loadable.")
        print("       Please ensure it's in your PySide6/plugins/imageformats/ directory or system Qt plugin path.")

    app.setStyle(QtWidgets.QStyleFactory.create("Fusion")) 

    images_dir = os.path.join(os.path.abspath("."), "images")
    os.makedirs(images_dir, exist_ok=True)
    
    icon_stroke_color = "rgba(200,200,205,220)" 
    icon_defs = {
        "dropzone-icon-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12"/></svg>',
        "text-doc-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="1.5"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>',
        "stage-tab-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" /><polyline points="17 8 12 3 7 8" /><line x1="12" y1="3" x2="12" y2="15" /></svg>',
        "clipboard-tab-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/><rect x="8" y="2" width="8" height="4" rx="1" ry="1"/></svg>',
        "folder-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/></svg>',
        "copy-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>',
        "delete-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>',
        "add-to-stage-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline></svg>', 
        "clear-all-light.svg": f'<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="{icon_stroke_color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>'
    }
    for name, svg_content in icon_defs.items():
        path = os.path.join(images_dir, name)
        if not os.path.exists(path): # Only create if it doesn't exist, to preserve user's own icons if they replace them
            print(f"INFO: Creating dummy icon: {name}")
            with open(path, "w") as f: f.write(svg_content)
    
    widget = FilePillWidget()
    sys.exit(app.exec())