from PySide6 import QtWidgets, Qt<PERSON>ore, QtGui
import sys
import datetime
import os
import json

CONFIG_PATH = os.path.join(os.path.dirname(__file__), "widget_config.json")

# Function to get the path to bundled data files in PyInstaller
def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    base_path = getattr(sys, '_MEIPASS', os.path.dirname(__file__))
    return os.path.join(base_path, relative_path)

class SettingsDialog(QtWidgets.QDialog):
    def __init__(self, parent):
        super().__init__(parent)
        self.parent_widget = parent
        self.setWindowTitle("Widget Settings")
        # Remove the '?' help button on the title bar
        self.setWindowFlags(self.windowFlags() & ~QtCore.Qt.WindowContextHelpButtonHint)
        self.init_ui()

    def init_ui(self):
        layout = QtWidgets.QFormLayout(self)

        # Color picker button
        self.color_button = QtWidgets.QPushButton("Choose Text Color")
        self.color_button.clicked.connect(self.choose_color)
        layout.addRow("Text Color:", self.color_button)

        # Spacing controls
        self.day_spacing = QtWidgets.QSpinBox()
        self.day_spacing.setRange(0, 100)
        self.day_spacing.setValue(self.parent_widget.day_spacing)
        layout.addRow("Day Letter Spacing:", self.day_spacing)

        self.date_spacing = QtWidgets.QSpinBox()
        self.date_spacing.setRange(0, 50)
        self.date_spacing.setValue(self.parent_widget.date_spacing)
        layout.addRow("Date Letter Spacing:", self.date_spacing)

        self.time_spacing = QtWidgets.QSpinBox()
        self.time_spacing.setRange(0, 50)
        self.time_spacing.setValue(self.parent_widget.time_spacing)
        layout.addRow("Time Letter Spacing:", self.time_spacing)

        # Widget size controls
        size_layout = QtWidgets.QHBoxLayout()
        self.width_spin = QtWidgets.QSpinBox()
        self.width_spin.setRange(100, 1000)
        self.width_spin.setValue(self.parent_widget.widget_width)
        self.height_spin = QtWidgets.QSpinBox()
        self.height_spin.setRange(50, 800)
        self.height_spin.setValue(self.parent_widget.widget_height)
        size_layout.addWidget(QtWidgets.QLabel("W:"))
        size_layout.addWidget(self.width_spin)
        size_layout.addSpacing(20)
        size_layout.addWidget(QtWidgets.QLabel("H:"))
        size_layout.addWidget(self.height_spin)
        layout.addRow("Widget Size:", size_layout)

        # Buttons
        buttons = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Save | QtWidgets.QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.apply_settings)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)

    def choose_color(self):
        # Build a QColor from the parent widget's RGBA tuple
        r, g, b, a = self.parent_widget.text_color
        initial = QtGui.QColor(r, g, b, a)
        color = QtWidgets.QColorDialog.getColor(initial, self, "Select Text Color")
        if color.isValid():
            self.selected_color = color
            # Update button icon to preview selected color
            pix = QtGui.QPixmap(16, 16)
            pix.fill(color)
            self.color_button.setIcon(QtGui.QIcon(pix))
        else:
            self.selected_color = None

    def apply_settings(self):
        # Apply color if chosen
        if hasattr(self, "selected_color") and self.selected_color:
            r, g, b, a = self.selected_color.getRgb()
            self.parent_widget.text_color = (r, g, b, a)
        # Apply spacings
        self.parent_widget.day_spacing = self.day_spacing.value()
        self.parent_widget.date_spacing = self.date_spacing.value()
        self.parent_widget.time_spacing = self.time_spacing.value()
        # Apply size
        self.parent_widget.widget_width = self.width_spin.value()
        self.parent_widget.widget_height = self.height_spin.value()
        self.parent_widget.resize(
            self.parent_widget.widget_width,
            self.parent_widget.widget_height
        )
        # Refresh styles and layout
        self.parent_widget.update_styles()
        # Save to config
        self.parent_widget.save_settings()
        self.accept()


class TimeWidget(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        # Default settings
        self.text_color = (0, 0, 0, 230)  # black with some transparency
        self.day_spacing = 40
        self.date_spacing = 4
        self.time_spacing = 6
        self.widget_width = 600
        self.widget_height = 200

        # Try loading saved settings
        self.load_settings()
        self.init_ui()

    def load_settings(self):
        if os.path.exists(CONFIG_PATH):
            try:
                with open(CONFIG_PATH, "r") as f:
                    data = json.load(f)
                    self.text_color = tuple(data.get("text_color", self.text_color))
                    self.day_spacing = data.get("day_spacing", self.day_spacing)
                    self.date_spacing = data.get("date_spacing", self.date_spacing)
                    self.time_spacing = data.get("time_spacing", self.time_spacing)
                    self.widget_width = data.get("widget_width", self.widget_width)
                    self.widget_height = data.get("widget_height", self.widget_height)
            except Exception:
                pass  # If loading fails, keep defaults

    def save_settings(self):
        data = {
            "text_color": list(self.text_color),
            "day_spacing": self.day_spacing,
            "date_spacing": self.date_spacing,
            "time_spacing": self.time_spacing,
            "widget_width": self.widget_width,
            "widget_height": self.widget_height
        }
        try:
            with open(CONFIG_PATH, "w") as f:
                json.dump(data, f)
        except Exception:
            pass  # Ignore save errors

    def init_ui(self):
        # Remove window borders and treat as a tool window (not always on top)
        self.setWindowFlags(
            QtCore.Qt.FramelessWindowHint
            | QtCore.Qt.Tool
        )
        # Fully transparent background
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)

        # Initial size
        self.resize(self.widget_width, self.widget_height)

        # Vertical layout, no extra margins
        self.layout = QtWidgets.QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(4)

        # Add stretch to center vertically
        self.layout.addStretch()

        # Load Anurati font if present
        # Use resource_path to find the font file correctly in PyInstaller bundle
        font_path = resource_path("Anurati-Regular.otf")
        if os.path.exists(font_path):
            font_id = QtGui.QFontDatabase.addApplicationFont(font_path)
            families = QtGui.QFontDatabase.applicationFontFamilies(font_id)
            self.font_family = families[0] if families else "Segoe UI"
        else:
            self.font_family = "Segoe UI"

        # Day label (e.g. "THURSDAY")
        self.day_label = QtWidgets.QLabel()
        self.day_label.setAlignment(QtCore.Qt.AlignCenter)
        self.layout.addWidget(self.day_label)

        # Date label (e.g. "20 APRIL, 2023.")
        self.date_label = QtWidgets.QLabel()
        self.date_label.setAlignment(QtCore.Qt.AlignCenter)
        self.layout.addWidget(self.date_label)

        # Time label (e.g. "- 12:06 -")
        self.time_label = QtWidgets.QLabel()
        self.time_label.setAlignment(QtCore.Qt.AlignCenter)
        self.layout.addWidget(self.time_label)

        # Add stretch at bottom to complete vertical centering
        self.layout.addStretch()

        # Timer to update every second
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.update_display)
        self.timer.start(1000)

        self.update_display()
        self.update_styles()
        self.show()

        # Variables for dragging the widget
        self.dragging = False
        self.drag_offset = QtCore.QPoint()

    def update_display(self):
        now = datetime.datetime.now()
        # Day in uppercase
        day_str = now.strftime('%A').upper()
        self.day_label.setText(day_str)

        # Date in format "20 APRIL, 2023."
        date_str = now.strftime('%d %B, %Y').upper() + "."
        self.date_label.setText(date_str)

        # Time in format "- HH:MM -"
        time_str = now.strftime('%H:%M')
        self.time_label.setText(f"- {time_str} -")

    def update_styles(self):
        # Apply dynamic font, size, color, and spacing to each label

        # Day label
        day_font = QtGui.QFont(self.font_family)
        day_font.setPointSize(72)
        day_font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, self.day_spacing)
        self.day_label.setFont(day_font)
        r, g, b, a = self.text_color
        self.day_label.setStyleSheet(f"""
            QLabel {{
                color: rgba({r}, {g}, {b}, {a});
                background: transparent;
            }}
        """)

        # Date label
        date_font = QtGui.QFont(self.font_family)
        date_font.setPointSize(18)
        date_font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, self.date_spacing)
        self.date_label.setFont(date_font)
        r, g, b, a = self.text_color
        self.date_label.setStyleSheet(f"""
            QLabel {{
                color: rgba({r}, {g}, {b}, {int(a * 0.78)});
                background: transparent;
            }}
        """)

        # Time label
        time_font = QtGui.QFont(self.font_family)
        time_font.setPointSize(20)
        time_font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, self.time_spacing)
        self.time_label.setFont(time_font)
        r, g, b, a = self.text_color
        self.time_label.setStyleSheet(f"""
            QLabel {{
                color: rgba({r}, {g}, {b}, {int(a * 0.87)});
                background: transparent;
            }}
        """)

        # Ensure widget is the correct size
        self.resize(self.widget_width, self.widget_height)

    def contextMenuEvent(self, event):
        menu = QtWidgets.QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background: rgba(255, 255, 255, 240);
                border: 1px solid rgba(0, 0, 0, 50);
                border-radius: 4px;
                padding: 4px;
            }
            QMenu::item {
                color: rgba(0, 0, 0, 230);
                padding: 4px 20px;
                font-size: 12px;
            }
            QMenu::item:selected {
                background: rgba(0, 0, 0, 20);
            }
        """)

        settings_action = menu.addAction("Settings")
        settings_action.triggered.connect(self.open_settings)

        menu.addSeparator()

        close_action = menu.addAction("Close")
        # Close should quit the entire application
        close_action.triggered.connect(QtWidgets.QApplication.instance().quit)

        menu.exec(event.globalPos())

    def open_settings(self):
        dlg = SettingsDialog(self)
        dlg.exec()

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.dragging = True
            self.drag_offset = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        if self.dragging and event.buttons() == QtCore.Qt.LeftButton:
            new_pos = event.globalPosition().toPoint() - self.drag_offset
            self.move(new_pos)
            event.accept()

    def mouseReleaseEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.dragging = False
            event.accept()

if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    widget = TimeWidget()
    sys.exit(app.exec())
