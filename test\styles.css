ResourceWidget {
    background-color: transparent;
}

QFrame#container {
    /* background-color: rgba(250, 250, 250, 230); */
    background-color: transparent; /* Make background transparent */
    border-radius: 12px;
}

QProgressBar {
    background-color: #E0E0E0;
    border-radius: 8px;
    text-align: center;
    color: #000;
    height: 16px; /* Added height here for consistency */
    /* border: 1px solid red; */ /* Removed test border */
}

/* Removed generic QProgressBar::chunk rule */
/*
QProgressBar::chunk {
    border-radius: 8px;
    background-color: purple;
}
*/

QProgressBar::chunk#battery_bar {
    background-color: #3B82F6; /* blue */
    border-radius: 8px;
}

QProgressBar::chunk#cpu_bar {
    background-color: #F97316; /* orange */
    border-radius: 8px;
}

QProgressBar::chunk#ram_bar {
    background-color: #10B981; /* green */
    border-radius: 8px;
}

/* Style for the battery bar when charging */
QProgressBar::chunk#battery_bar_charging {
    background-color: #10B981; /* green */
    border-radius: 8px;
    /* Removed unsupported CSS animation */
} 