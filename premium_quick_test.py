#!/usr/bin/env python3
"""
Premium Quick Test for the Power Notification Widget
Demonstrates the premium notification with all advanced features.
"""

from PySide6 import QtWidgets, QtCore
import sys
from premium_power_notification_widget import PremiumPowerNotificationWidget


def main():
    """Quick test of the premium notification."""
    app = QtWidgets.QApplication(sys.argv)
    
    # Create premium notification widget
    notification = PremiumPowerNotificationWidget()
    
    print("🚀 Showing premium power notification with advanced animations...")
    print("✨ Features: Glass morphism, multi-phase animations, micro-interactions")
    print("🎯 Click the notification to dismiss it")
    print("⏱️  Auto-dismiss after 4 seconds")
    
    # Show notification
    notification.show_notification()
    
    # Auto-exit after 8 seconds to see full animation cycle
    QtCore.QTimer.singleShot(8000, app.quit)
    
    # Run app
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
