#!/usr/bin/env python3
"""
Test script for the Power Notification Widget
This script demonstrates the notification system with manual triggers.
"""

from PySide6 import QtWidgets, QtCore, QtGui
import sys
import os
from power_notification_widget import PowerNotificationWidget


class TestWindow(QtWidgets.QWidget):
    """Simple test window with buttons to trigger notifications."""
    
    def __init__(self):
        super().__init__()
        self.notification_widget = PowerNotificationWidget()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the test UI."""
        self.setWindowTitle("Power Notification Test")
        self.setGeometry(100, 100, 300, 200)
        
        layout = QtWidgets.QVBoxLayout(self)
        
        # Title
        title = QtWidgets.QLabel("Power Notification System Test")
        title.setAlignment(QtCore.Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Test notification button
        test_btn = QtWidgets.QPushButton("Show Test Notification")
        test_btn.clicked.connect(self.show_test_notification)
        test_btn.setStyleSheet("padding: 10px; font-size: 14px;")
        layout.addWidget(test_btn)
        
        # Start monitoring button
        monitor_btn = QtWidgets.QPushButton("Start Power Monitoring")
        monitor_btn.clicked.connect(self.start_monitoring)
        monitor_btn.setStyleSheet("padding: 10px; font-size: 14px;")
        layout.addWidget(monitor_btn)
        
        # Stop monitoring button
        stop_btn = QtWidgets.QPushButton("Stop Power Monitoring")
        stop_btn.clicked.connect(self.stop_monitoring)
        stop_btn.setStyleSheet("padding: 10px; font-size: 14px;")
        layout.addWidget(stop_btn)
        
        # Battery status button
        status_btn = QtWidgets.QPushButton("Show Battery Status")
        status_btn.clicked.connect(self.show_battery_status)
        status_btn.setStyleSheet("padding: 10px; font-size: 14px;")
        layout.addWidget(status_btn)
        
        # Status label
        self.status_label = QtWidgets.QLabel("Ready")
        self.status_label.setAlignment(QtCore.Qt.AlignCenter)
        self.status_label.setStyleSheet("margin: 10px; color: #666;")
        layout.addWidget(self.status_label)
        
    def show_test_notification(self):
        """Show a test notification."""
        self.status_label.setText("Showing test notification...")
        self.notification_widget.trigger_test_notification()
        
        # Reset status after delay
        QtCore.QTimer.singleShot(2000, lambda: self.status_label.setText("Ready"))
        
    def start_monitoring(self):
        """Start power monitoring."""
        self.notification_widget.start_monitoring()
        self.status_label.setText("Power monitoring started")
        
    def stop_monitoring(self):
        """Stop power monitoring."""
        self.notification_widget.stop_monitoring()
        self.status_label.setText("Power monitoring stopped")
        
    def show_battery_status(self):
        """Show current battery status."""
        battery_info = self.notification_widget.power_monitor.get_current_battery_info()
        
        if battery_info:
            status = "Connected" if battery_info['power_plugged'] else "Disconnected"
            message = f"Battery: {battery_info['percent']}%\nPower: {status}"
            self.status_label.setText(f"Battery: {battery_info['percent']}% | Power: {status}")
        else:
            message = "Battery information not available"
            self.status_label.setText("No battery info available")
            
        QtWidgets.QMessageBox.information(self, "Battery Status", message)
        
    def closeEvent(self, event):
        """Handle window close."""
        self.notification_widget.stop_monitoring()
        self.notification_widget.close()
        event.accept()


def main():
    """Main entry point for testing."""
    app = QtWidgets.QApplication(sys.argv)
    
    # Create test window
    test_window = TestWindow()
    test_window.show()
    
    print("Power Notification Test Window opened!")
    print("Use the buttons to test the notification system.")
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
