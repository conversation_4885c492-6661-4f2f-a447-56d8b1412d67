from PySide6 import QtWidgets, Qt<PERSON>ore, QtGui
import sys
import os
import json
import datetime
from power_monitor_service import PowerMonitorService


class PremiumPowerNotificationWidget(QtWidgets.QWidget):
    """Premium power connection notification widget with advanced animations and glass morphism."""
    
    def __init__(self):
        super().__init__()
        
        # Load configuration
        self.config = self.load_config()
        
        # Initialize power monitor
        self.power_monitor = PowerMonitorService(self.config['behavior']['polling_interval'])
        self.power_monitor.power_connected.connect(self.show_notification)
        
        # Animation objects
        self.entrance_animation_group = None
        self.exit_animation_group = None
        self.icon_animation = None
        self.glow_animation = None
        
        # Auto-dismiss timer
        self.dismiss_timer = QtCore.QTimer(self)
        self.dismiss_timer.setSingleShot(True)
        self.dismiss_timer.timeout.connect(self.hide_notification)
        
        # Update timer for dynamic content
        self.update_timer = QtCore.QTimer(self)
        self.update_timer.timeout.connect(self.update_dynamic_content)
        
        # State tracking
        self.is_visible = False
        self.is_animating = False
        self.current_theme = self.detect_system_theme()
        
        self.init_ui()
        
    def load_config(self):
        """Load premium configuration from JSON file."""
        config_path = os.path.join(os.path.dirname(__file__), "power_notification_config.json")
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()
            
    def get_default_config(self):
        """Return default premium configuration."""
        return {
            "visual_design": {
                "dimensions": {"width": 380, "height": 90, "border_radius": 18},
                "colors": {
                    "dark_theme": {
                        "background": "rgba(28, 28, 30, 0.95)",
                        "text_primary": "#FFFFFF",
                        "text_secondary": "rgba(255, 255, 255, 0.7)",
                        "border": "rgba(255, 255, 255, 0.15)"
                    },
                    "accent_colors": {"success_green": "#34C759"}
                },
                "shadows": {"primary": "0 10px 25px rgba(0,0,0,0.15)", "blur_radius": 20},
                "typography": {
                    "primary_text": {"size": 17, "weight": "500"},
                    "secondary_text": {"size": 14, "weight": "400"}
                }
            },
            "animation_system": {
                "entrance": {"total_duration": 1100},
                "exit": {"duration": 400}
            },
            "behavior": {"display_duration": 4000, "notification_position": {"y": 24}}
        }
        
    def detect_system_theme(self):
        """Detect system theme (simplified)."""
        # For now, default to dark theme
        # In a real implementation, this would check system settings
        return "dark_theme"
        
    def init_ui(self):
        """Initialize the premium user interface."""
        # Window setup
        self.setWindowFlags(
            QtCore.Qt.FramelessWindowHint |
            QtCore.Qt.Tool |
            QtCore.Qt.WindowStaysOnTopHint
        )
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.setAttribute(QtCore.Qt.WA_NoSystemBackground)
        
        # Set dimensions
        dimensions = self.config['visual_design']['dimensions']
        self.setFixedSize(dimensions['width'], dimensions['height'])
        
        # Create main layout
        self.main_layout = QtWidgets.QHBoxLayout(self)
        padding = self.config['content_system']['layout']['padding']
        self.main_layout.setContentsMargins(
            padding['horizontal'], padding['vertical'],
            padding['horizontal'], padding['vertical']
        )
        self.main_layout.setSpacing(self.config['content_system']['layout']['spacing']['icon_to_text'])
        
        # Create icon container
        self.icon_container = QtWidgets.QWidget()
        self.icon_container.setFixedSize(40, 40)
        self.icon_layout = QtWidgets.QVBoxLayout(self.icon_container)
        self.icon_layout.setContentsMargins(0, 0, 0, 0)
        
        # Icon label
        self.icon_label = QtWidgets.QLabel()
        self.icon_label.setAlignment(QtCore.Qt.AlignCenter)
        self.load_premium_icon()
        self.icon_layout.addWidget(self.icon_label)
        
        self.main_layout.addWidget(self.icon_container)
        
        # Create text container
        self.text_container = QtWidgets.QWidget()
        self.text_layout = QtWidgets.QVBoxLayout(self.text_container)
        self.text_layout.setContentsMargins(0, 0, 0, 0)
        self.text_layout.setSpacing(self.config['content_system']['layout']['spacing']['text_line_spacing'])
        
        # Primary text label
        self.primary_text_label = QtWidgets.QLabel("Power Connected")
        self.primary_text_label.setObjectName("primary_text")
        
        # Secondary text label
        self.secondary_text_label = QtWidgets.QLabel()
        self.secondary_text_label.setObjectName("secondary_text")
        self.update_secondary_text()
        
        self.text_layout.addWidget(self.primary_text_label)
        self.text_layout.addWidget(self.secondary_text_label)
        self.text_layout.addStretch()
        
        self.main_layout.addWidget(self.text_container)
        self.main_layout.addStretch()
        
        # Apply premium styling
        self.apply_premium_styles()
        
        # Position widget off-screen initially
        self.position_widget(hidden=True)
        
        # Hide initially
        self.hide()
        
    def load_premium_icon(self):
        """Load premium charging icon with glow effect."""
        icon_path = os.path.join(os.path.dirname(__file__), "images", "battery-bolt.svg")
        icon_size = self.config['content_system']['icon']['size']
        
        if os.path.exists(icon_path):
            # Load and style the SVG icon
            pixmap = QtGui.QPixmap(icon_path)
            if not pixmap.isNull():
                # Create colored version
                accent_color = self.config['visual_design']['colors']['accent_colors']['success_green']
                colored_pixmap = self.create_colored_icon(pixmap, accent_color)
                scaled_pixmap = colored_pixmap.scaled(
                    icon_size, icon_size,
                    QtCore.Qt.KeepAspectRatio,
                    QtCore.Qt.SmoothTransformation
                )
                self.icon_label.setPixmap(scaled_pixmap)
            else:
                self.set_premium_fallback_icon()
        else:
            self.set_premium_fallback_icon()
            
        # Apply glow effect to icon
        self.apply_icon_glow_effect()
        
    def create_colored_icon(self, pixmap, color):
        """Create a colored version of the icon."""
        colored_pixmap = QtGui.QPixmap(pixmap.size())
        colored_pixmap.fill(QtCore.Qt.transparent)
        
        painter = QtGui.QPainter(colored_pixmap)
        painter.setCompositionMode(QtGui.QPainter.CompositionMode_SourceOver)
        painter.drawPixmap(0, 0, pixmap)
        painter.setCompositionMode(QtGui.QPainter.CompositionMode_SourceIn)
        painter.fillRect(colored_pixmap.rect(), QtGui.QColor(color))
        painter.end()
        
        return colored_pixmap
        
    def set_premium_fallback_icon(self):
        """Set premium fallback icon."""
        accent_color = self.config['visual_design']['colors']['accent_colors']['success_green']
        icon_size = self.config['content_system']['icon']['size']
        
        self.icon_label.setText("⚡")
        self.icon_label.setStyleSheet(f"""
            QLabel {{
                color: {accent_color};
                font-size: {icon_size}px;
                font-weight: bold;
            }}
        """)
        
    def apply_icon_glow_effect(self):
        """Apply glow effect to the icon."""
        glow_effect = QtWidgets.QGraphicsDropShadowEffect()
        glow_effect.setBlurRadius(self.config['content_system']['icon']['glow_radius'])
        glow_effect.setColor(QtGui.QColor(self.config['visual_design']['colors']['accent_colors']['success_green']))
        glow_effect.setOffset(0, 0)
        self.icon_label.setGraphicsEffect(glow_effect)

    def apply_premium_styles(self):
        """Apply premium glass morphism styling."""
        theme_colors = self.config['visual_design']['colors'][self.current_theme]
        typography = self.config['visual_design']['typography']
        border_radius = self.config['visual_design']['dimensions']['border_radius']

        # Main widget styling with glass morphism
        main_style = f"""
        PremiumPowerNotificationWidget {{
            background-color: {theme_colors['background']};
            border-radius: {border_radius}px;
            border: 1px solid {theme_colors['border']};
        }}
        """

        # Primary text styling
        primary_text_style = f"""
        QLabel#primary_text {{
            color: {theme_colors['text_primary']};
            font-size: {typography['primary_text']['size']}px;
            font-weight: {typography['primary_text']['weight']};
            background: transparent;
            border: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
        }}
        """

        # Secondary text styling
        secondary_text_style = f"""
        QLabel#secondary_text {{
            color: {theme_colors['text_secondary']};
            font-size: {typography['secondary_text']['size']}px;
            font-weight: {typography['secondary_text']['weight']};
            background: transparent;
            border: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
        }}
        """

        combined_style = main_style + primary_text_style + secondary_text_style
        self.setStyleSheet(combined_style)

        # Apply premium drop shadow
        self.apply_premium_shadow()

    def apply_premium_shadow(self):
        """Apply premium drop shadow effect."""
        shadow = QtWidgets.QGraphicsDropShadowEffect()
        shadow.setBlurRadius(self.config['visual_design']['shadows']['blur_radius'])
        shadow.setColor(QtGui.QColor(0, 0, 0, 40))  # Subtle shadow
        shadow.setOffset(0, 8)  # Downward offset for depth
        self.setGraphicsEffect(shadow)

    def paintEvent(self, event):
        """Custom paint event for premium glass morphism background."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # Get theme colors
        theme_colors = self.config['visual_design']['colors'][self.current_theme]
        border_radius = self.config['visual_design']['dimensions']['border_radius']

        # Draw glass morphism background
        rect = self.rect().adjusted(1, 1, -1, -1)  # Adjust for border

        # Background with slight transparency
        bg_color = QtGui.QColor(theme_colors['background'])
        painter.setBrush(QtGui.QBrush(bg_color))

        # Border
        border_color = QtGui.QColor(theme_colors['border'])
        pen = QtGui.QPen(border_color)
        pen.setWidth(1)
        painter.setPen(pen)

        # Draw rounded rectangle
        painter.drawRoundedRect(rect, border_radius, border_radius)

        super().paintEvent(event)

    def update_secondary_text(self):
        """Update secondary text with dynamic content."""
        current_time = datetime.datetime.now().strftime("%I:%M %p")
        battery_info = self.power_monitor.get_current_battery_info()

        if battery_info:
            secondary_text = f"Battery: {battery_info['percent']}% • {current_time}"
        else:
            secondary_text = f"Power Status • {current_time}"

        self.secondary_text_label.setText(secondary_text)

    def update_dynamic_content(self):
        """Update dynamic content while notification is visible."""
        if self.is_visible:
            self.update_secondary_text()

    def position_widget(self, hidden=False):
        """Position the widget on screen with multi-monitor support."""
        # Get screen geometry for the active screen
        screen = QtWidgets.QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # Calculate horizontal center
        x = screen_geometry.x() + (screen_geometry.width() - self.width()) // 2

        # Calculate vertical position
        if hidden:
            y = screen_geometry.y() - self.height() - 50  # Well above screen
        else:
            y = screen_geometry.y() + self.config['behavior']['notification_position']['y']

        self.move(x, y)

    def show_notification(self):
        """Show notification with premium multi-phase animation sequence."""
        if self.is_visible or self.is_animating:
            return

        self.is_animating = True

        # Update content before showing
        battery_info = self.power_monitor.get_current_battery_info()
        if battery_info:
            self.primary_text_label.setText(battery_info['status_message'])
        self.update_secondary_text()

        # Position widget above screen
        self.position_widget(hidden=True)
        self.show()
        self.setWindowOpacity(0.0)

        # Start dynamic content updates
        self.update_timer.start(1000)  # Update every second

        # Create multi-phase entrance animation
        self.create_entrance_animation_sequence()

    def create_entrance_animation_sequence(self):
        """Create the premium multi-phase entrance animation."""
        animation_config = self.config['animation_system']['entrance']

        # Phase 1: Slide down with spring physics
        self.entrance_animation_group = QtCore.QSequentialAnimationGroup()

        # Slide and fade animation (parallel)
        slide_fade_group = QtCore.QParallelAnimationGroup()

        # Slide animation
        slide_animation = QtCore.QPropertyAnimation(self, b"geometry")
        slide_animation.setDuration(animation_config['phase_1_duration'])
        slide_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)

        # Calculate positions
        start_rect = self.geometry()
        screen = QtWidgets.QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        x = screen_geometry.x() + (screen_geometry.width() - self.width()) // 2
        y = screen_geometry.y() + self.config['behavior']['notification_position']['y']
        end_rect = QtCore.QRect(x, y, self.width(), self.height())

        slide_animation.setStartValue(start_rect)
        slide_animation.setEndValue(end_rect)

        # Fade animation
        fade_animation = QtCore.QPropertyAnimation(self, b"windowOpacity")
        fade_animation.setDuration(animation_config['phase_1_duration'])
        fade_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        fade_animation.setStartValue(0.0)
        fade_animation.setEndValue(0.98)

        slide_fade_group.addAnimation(slide_animation)
        slide_fade_group.addAnimation(fade_animation)

        # Phase 2: Subtle scale "pop" effect
        scale_animation = QtCore.QPropertyAnimation(self, b"geometry")
        scale_animation.setDuration(animation_config['phase_2_duration'])
        scale_animation.setEasingCurve(QtCore.QEasingCurve.OutBack)

        # Slightly larger then back to normal
        pop_rect = QtCore.QRect(x - 2, y - 1, self.width() + 4, self.height() + 2)
        scale_animation.setStartValue(end_rect)
        scale_animation.setKeyValueAt(0.5, pop_rect)
        scale_animation.setEndValue(end_rect)

        # Add phases to sequence
        self.entrance_animation_group.addAnimation(slide_fade_group)
        self.entrance_animation_group.addAnimation(scale_animation)

        # Connect completion
        self.entrance_animation_group.finished.connect(self.on_entrance_animation_finished)

        # Start the sequence
        self.entrance_animation_group.start()

        # Start micro-animations after a delay
        QtCore.QTimer.singleShot(200, self.start_micro_animations)

    def start_micro_animations(self):
        """Start micro-animations for icon and text."""
        # Icon scale animation
        self.icon_animation = QtCore.QPropertyAnimation(self.icon_container, b"geometry")
        self.icon_animation.setDuration(self.config['animation_system']['micro_animations']['icon_scale_duration'])
        self.icon_animation.setEasingCurve(QtCore.QEasingCurve.OutBack)

        # Icon scale from small to normal
        icon_rect = self.icon_container.geometry()
        small_rect = QtCore.QRect(
            icon_rect.x() + 4, icon_rect.y() + 4,
            icon_rect.width() - 8, icon_rect.height() - 8
        )

        self.icon_animation.setStartValue(small_rect)
        self.icon_animation.setEndValue(icon_rect)
        self.icon_animation.start()

        # Text fade-in with slight upward motion
        self.text_container.setProperty("opacity", 0.0)
        text_fade = QtCore.QPropertyAnimation(self.text_container, b"opacity")
        text_fade.setDuration(self.config['animation_system']['micro_animations']['text_fade_duration'])
        text_fade.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        text_fade.setStartValue(0.0)
        text_fade.setEndValue(1.0)
        text_fade.start()

        # Start icon pulse animation
        self.start_icon_pulse_animation()

    def start_icon_pulse_animation(self):
        """Start subtle pulse animation for the icon."""
        self.glow_animation = QtCore.QPropertyAnimation()
        self.glow_animation.setTargetObject(self.icon_label.graphicsEffect())
        self.glow_animation.setPropertyName(b"blurRadius")
        self.glow_animation.setDuration(self.config['content_system']['icon']['pulse_duration'])
        self.glow_animation.setEasingCurve(QtCore.QEasingCurve.InOutSine)
        self.glow_animation.setStartValue(4)
        self.glow_animation.setEndValue(8)
        self.glow_animation.setLoopCount(-1)  # Infinite loop
        self.glow_animation.start()

    def on_entrance_animation_finished(self):
        """Called when entrance animation sequence completes."""
        self.is_animating = False
        self.is_visible = True

        # Start auto-dismiss timer
        self.dismiss_timer.start(self.config['behavior']['display_duration'])

    def hide_notification(self):
        """Hide notification with premium exit animation."""
        if not self.is_visible or self.is_animating:
            return

        self.is_animating = True
        self.dismiss_timer.stop()
        self.update_timer.stop()

        # Stop any ongoing animations
        if self.glow_animation:
            self.glow_animation.stop()

        # Create exit animation group
        self.exit_animation_group = QtCore.QParallelAnimationGroup()

        # Fade out animation
        fade_out = QtCore.QPropertyAnimation(self, b"windowOpacity")
        fade_out.setDuration(self.config['animation_system']['exit']['fade_duration'])
        fade_out.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        fade_out.setStartValue(self.windowOpacity())
        fade_out.setEndValue(0.0)

        # Slight upward drift
        current_pos = self.geometry()
        drift_pos = QtCore.QRect(
            current_pos.x(), current_pos.y() - 10,
            current_pos.width(), current_pos.height()
        )

        drift_animation = QtCore.QPropertyAnimation(self, b"geometry")
        drift_animation.setDuration(self.config['animation_system']['exit']['duration'])
        drift_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        drift_animation.setStartValue(current_pos)
        drift_animation.setEndValue(drift_pos)

        self.exit_animation_group.addAnimation(fade_out)
        self.exit_animation_group.addAnimation(drift_animation)

        # Connect completion
        self.exit_animation_group.finished.connect(self.on_exit_animation_finished)

        # Start exit animation
        self.exit_animation_group.start()

    def on_exit_animation_finished(self):
        """Called when exit animation completes."""
        self.hide()
        self.is_animating = False
        self.is_visible = False

    def mousePressEvent(self, event):
        """Handle mouse press with premium click feedback."""
        if event.button() == QtCore.Qt.LeftButton:
            # Brief scale down effect
            self.create_click_feedback()
            # Delay hide to show feedback
            QtCore.QTimer.singleShot(100, self.hide_notification)
            event.accept()

    def create_click_feedback(self):
        """Create brief click feedback animation."""
        click_scale = self.config['animation_system']['interactive']['click_scale']
        duration = self.config['animation_system']['interactive']['transition_duration']

        # Scale down briefly
        current_rect = self.geometry()
        scale_down_rect = QtCore.QRect(
            current_rect.x() + 2, current_rect.y() + 1,
            int(current_rect.width() * click_scale), int(current_rect.height() * click_scale)
        )

        click_animation = QtCore.QPropertyAnimation(self, b"geometry")
        click_animation.setDuration(duration)
        click_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        click_animation.setStartValue(current_rect)
        click_animation.setKeyValueAt(0.5, scale_down_rect)
        click_animation.setEndValue(current_rect)
        click_animation.start()

    def enterEvent(self, event):
        """Handle mouse enter with premium hover effect."""
        if self.is_visible and not self.is_animating:
            self.create_hover_effect(True)
        event.accept()

    def leaveEvent(self, event):
        """Handle mouse leave."""
        if self.is_visible and not self.is_animating:
            self.create_hover_effect(False)
        event.accept()

    def create_hover_effect(self, hover_in):
        """Create premium hover effect."""
        hover_scale = self.config['animation_system']['interactive']['hover_scale']
        duration = self.config['animation_system']['interactive']['transition_duration']

        current_rect = self.geometry()

        if hover_in:
            # Scale up slightly
            hover_rect = QtCore.QRect(
                current_rect.x() - 1, current_rect.y() - 1,
                int(current_rect.width() * hover_scale), int(current_rect.height() * hover_scale)
            )
        else:
            # Return to normal
            screen = QtWidgets.QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            x = screen_geometry.x() + (screen_geometry.width() - self.width()) // 2
            y = screen_geometry.y() + self.config['behavior']['notification_position']['y']
            hover_rect = QtCore.QRect(x, y, self.width(), self.height())

        hover_animation = QtCore.QPropertyAnimation(self, b"geometry")
        hover_animation.setDuration(duration)
        hover_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        hover_animation.setStartValue(current_rect)
        hover_animation.setEndValue(hover_rect)
        hover_animation.start()

    def start_monitoring(self):
        """Start power monitoring."""
        self.power_monitor.start_monitoring()

    def stop_monitoring(self):
        """Stop power monitoring."""
        self.power_monitor.stop_monitoring()

    def trigger_test_notification(self):
        """Trigger a test notification."""
        self.show_notification()

    def closeEvent(self, event):
        """Handle widget close event."""
        self.stop_monitoring()
        self.update_timer.stop()

        # Stop all animations
        if self.entrance_animation_group:
            self.entrance_animation_group.stop()
        if self.exit_animation_group:
            self.exit_animation_group.stop()
        if self.icon_animation:
            self.icon_animation.stop()
        if self.glow_animation:
            self.glow_animation.stop()

        self.dismiss_timer.stop()
        event.accept()
