import sys
import random
from PySide6 import QtWidgets, Qt<PERSON>ore, QtGui

# --- Mock Power Monitor Service (for testing purposes) ---
# In a real app, this would be your actual power monitoring logic.
class MockPowerMonitorService(QtCore.QObject):
    """A mock service to simulate power connection events and battery updates."""
    power_connected = QtCore.Signal()

    def __init__(self):
        super().__init__()
        self._is_connected = False
        self._battery_percent = 85  # Start with the example percentage

    def toggle_power_status(self):
        """Simulates plugging/unplugging the power."""
        self._is_connected = not self._is_connected
        if self._is_connected:
            self._battery_percent = random.randint(10, 95)  # Simulate a random percentage on connect
            self.power_connected.emit()

    def get_current_battery_info(self):
        """Returns mock battery info."""
        if self._is_connected:
            return {
                "percent": self._battery_percent,
                "status_message": "Charging"
            }
        return None


# --- Custom Widget for the Dynamic Battery Icon ---
class BatteryIconWidget(QtWidgets.QWidget):
    """A custom widget to draw a dynamic, fillable battery icon."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._charge_level = 85  # 0 to 100
        self.setFixedSize(28, 14)

    def set_charge_level(self, level):
        self._charge_level = max(0, min(100, level))
        self.update()  # Trigger a repaint

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect().adjusted(0, 0, -3, 0)  # Main body rect, leaving space for the terminal
        border_radius = 4

        # Draw battery outline
        pen = QtGui.QPen(QtGui.QColor("#34C759"), 1.5)
        painter.setPen(pen)
        painter.setBrush(QtCore.Qt.NoBrush)
        painter.drawRoundedRect(rect, border_radius, border_radius)
        
        # Draw battery terminal
        terminal_rect = QtCore.QRect(rect.right(), rect.center().y() - 3, 2, 6)
        painter.drawRoundedRect(terminal_rect, 1, 1)

        # Draw battery fill
        if self._charge_level > 5: # Only draw fill if there's enough charge
            fill_rect = rect.adjusted(3, 3, -3, -3)
            fill_width = int(fill_rect.width() * (self._charge_level / 100.0))
            fill_rect.setWidth(fill_width)

            painter.setPen(QtCore.Qt.NoPen)
            painter.setBrush(QtGui.QColor("#34C759"))
            painter.drawRoundedRect(fill_rect, border_radius / 2, border_radius / 2)


# --- The Main Redesigned Notification Widget ---
class ChargingNotificationWidget(QtWidgets.QWidget):
    """
    A sleek, pill-shaped notification widget inspired by modern OS designs.
    Displays charging status with premium, fluid animations.
    """
    def __init__(self, power_monitor):
        super().__init__()
        
        self.power_monitor = power_monitor
        self.power_monitor.power_connected.connect(self.show_notification)
        
        # Animation and timers
        self.animation_group = None
        self.dismiss_timer = QtCore.QTimer(self, singleShot=True)
        self.dismiss_timer.timeout.connect(self.hide_notification)

        self.init_ui()
        self.init_animations()

    def init_ui(self):
        # Window Flags: Frameless, on top, and translucent background
        self.setWindowFlags(
            QtCore.Qt.FramelessWindowHint |
            QtCore.Qt.Tool |
            QtCore.Qt.WindowStaysOnTopHint
        )
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.setAttribute(QtCore.Qt.WA_NoSystemBackground)
        
        self.setFixedSize(260, 48)

        # Main layout: Left Text | Spacer | Right Group (Percentage + Icon)
        self.main_layout = QtWidgets.QHBoxLayout(self)
        self.main_layout.setContentsMargins(20, 0, 16, 0)
        self.main_layout.setSpacing(10)

        # --- Left Side ---
        self.status_label = QtWidgets.QLabel("Charging")
        self.status_label.setObjectName("statusLabel")

        # --- Right Side (in its own layout) ---
        self.right_container = QtWidgets.QWidget()
        self.right_layout = QtWidgets.QHBoxLayout(self.right_container)
        self.right_layout.setContentsMargins(0, 0, 0, 0)
        self.right_layout.setSpacing(6)

        self.percentage_label = QtWidgets.QLabel("85%")
        self.percentage_label.setObjectName("percentageLabel")

        self.battery_icon = BatteryIconWidget()

        self.right_layout.addWidget(self.percentage_label)
        self.right_layout.addWidget(self.battery_icon)
        
        # Assemble main layout
        self.main_layout.addWidget(self.status_label)
        self.main_layout.addStretch()
        self.main_layout.addWidget(self.right_container)
        
        self.apply_styles()
        self.position_widget(offscreen=True)
        
    def apply_styles(self):
        # Using a modern system font stack for a native feel.
        self.setStyleSheet("""
            QWidget {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            }
            #statusLabel {
                color: #E1E1E1;
                font-size: 16px;
                font-weight: 500;
            }
            #percentageLabel {
                color: #34C759;
                font-size: 16px;
                font-weight: 600;
            }
        """)

    def init_animations(self):
        # Set up a shadow effect for a sense of depth
        shadow = QtWidgets.QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(35)
        shadow.setColor(QtGui.QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)

    def paintEvent(self, event):
        # Custom paint event to draw the black pill background
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        
        background_color = QtGui.QColor("#1C1C1E")
        painter.setBrush(background_color)
        painter.setPen(QtCore.Qt.NoPen)
        
        path = QtGui.QPainterPath()
        path.addRoundedRect(self.rect(), self.height() / 2, self.height() / 2)
        painter.drawPath(path)
        
    def position_widget(self, offscreen=False):
        """Positions the widget at the top-center of the primary screen."""
        screen_geo = QtWidgets.QApplication.primaryScreen().availableGeometry()
        x = screen_geo.x() + (screen_geo.width() - self.width()) // 2
        
        if offscreen:
            # Start position is above the visible screen area
            y = screen_geo.y() - self.height()
        else:
            # Final position is near the top of the screen
            y = screen_geo.y() + 20
        
        self.move(x, y)

    def update_display(self):
        """Updates the content based on current power info."""
        info = self.power_monitor.get_current_battery_info()
        if info:
            self.status_label.setText(info["status_message"])
            self.percentage_label.setText(f"{info['percent']}%")
            self.battery_icon.set_charge_level(info["percent"])

    def show_notification(self):
        if self.isVisible(): # If already visible, just restart the timer
            self.dismiss_timer.start(4000)
            return

        self.update_display()
        self.position_widget(offscreen=True)
        self.setWindowOpacity(0.0)
        self.show()

        # Final on-screen position
        screen_geo = QtWidgets.QApplication.primaryScreen().availableGeometry()
        final_y = screen_geo.y() + 20
        final_pos = QtCore.QPoint(self.x(), final_y)

        # Entrance Animation: Drop in with a bounce
        anim_pos = QtCore.QPropertyAnimation(self, b"pos", self)
        anim_pos.setDuration(800)
        anim_pos.setStartValue(self.pos())
        anim_pos.setEndValue(final_pos)
        anim_pos.setEasingCurve(QtCore.QEasingCurve.OutElastic) # Springy/bouncy effect
        
        anim_opacity = QtCore.QPropertyAnimation(self, b"windowOpacity", self)
        anim_opacity.setDuration(400) # Faster fade in
        anim_opacity.setStartValue(0.0)
        anim_opacity.setEndValue(1.0)
        
        self.animation_group = QtCore.QParallelAnimationGroup(self)
        self.animation_group.addAnimation(anim_pos)
        self.animation_group.addAnimation(anim_opacity)
        self.animation_group.start(QtCore.QAbstractAnimation.DeleteWhenStopped)

        self.dismiss_timer.start(4000) # Auto-dismiss after 4 seconds

    def hide_notification(self):
        if not self.isVisible():
            return
            
        # Exit Animation: Slide up and fade out
        anim_pos = QtCore.QPropertyAnimation(self, b"pos", self)
        anim_pos.setDuration(300)
        anim_pos.setEndValue(QtCore.QPoint(self.x(), self.y() - 20))
        anim_pos.setEasingCurve(QtCore.QEasingCurve.InQuad)
        
        anim_opacity = QtCore.QPropertyAnimation(self, b"windowOpacity", self)
        anim_opacity.setDuration(300)
        anim_opacity.setEndValue(0.0)
        anim_opacity.setEasingCurve(QtCore.QEasingCurve.InQuad)

        self.animation_group = QtCore.QParallelAnimationGroup(self)
        self.animation_group.addAnimation(anim_pos)
        self.animation_group.addAnimation(anim_opacity)
        self.animation_group.finished.connect(self.hide)
        self.animation_group.start(QtCore.QAbstractAnimation.DeleteWhenStopped)
    
    def mousePressEvent(self, event):
        """Dismiss on click."""
        self.hide_notification()
        event.accept()

    def enterEvent(self, event):
        """Subtle lift on hover."""
        self.animation_group.stop() # Stop any ongoing animation
        anim_pos = QtCore.QPropertyAnimation(self, b"pos", self)
        anim_pos.setDuration(200)
        anim_pos.setEndValue(QtCore.QPoint(self.x(), self.y() - 3))
        anim_pos.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        anim_pos.start(QtCore.QAbstractAnimation.DeleteWhenStopped)
        event.accept()

    def leaveEvent(self, event):
        """Return to original position on hover leave."""
        self.animation_group.stop()
        screen_geo = QtWidgets.QApplication.primaryScreen().availableGeometry()
        final_y = screen_geo.y() + 20
        anim_pos = QtCore.QPropertyAnimation(self, b"pos", self)
        anim_pos.setDuration(300)
        anim_pos.setEndValue(QtCore.QPoint(self.x(), final_y))
        anim_pos.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        anim_pos.start(QtCore.QAbstractAnimation.DeleteWhenStopped)
        event.accept()

# --- Main execution block for testing ---
if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    
    # Use the Mock Service for this demo
    mock_power_service = MockPowerMonitorService()

    # The main notification widget
    notification = ChargingNotificationWidget(mock_power_service)

    # A simple test window with a button to trigger the notification
    test_window = QtWidgets.QMainWindow()
    central_widget = QtWidgets.QWidget()
    layout = QtWidgets.QVBoxLayout(central_widget)
    
    label = QtWidgets.QLabel("Click the button to simulate plugging in a power cable.")
    label.setAlignment(QtCore.Qt.AlignCenter)
    
    button = QtWidgets.QPushButton("Test Notification (Simulate Power Connect)")
    button.clicked.connect(mock_power_service.toggle_power_status)
    button.setMinimumHeight(40)
    
    layout.addWidget(label)
    layout.addWidget(button)
    
    test_window.setCentralWidget(central_widget)
    test_window.setWindowTitle("Test Controls")
    test_window.setGeometry(300, 300, 400, 150)
    test_window.show()

    sys.exit(app.exec())