#!/usr/bin/env python3
"""
Battery Percentage Test
Verify the notification shows the correct battery percentage from the start.
"""

from PySide6 import QtWidgets, QtCore
import sys
from macos_power_notification import MacOSPowerNotification


def main():
    """Test battery percentage accuracy."""
    app = QtWidgets.QApplication(sys.argv)
    
    # Create notification
    notification = MacOSPowerNotification()
    
    # Get actual battery info
    battery_info = notification.power_monitor.get_current_battery_info()
    
    print("🔋 Battery Percentage Test")
    if battery_info:
        print(f"📊 Actual Battery Level: {battery_info['percent']}%")
        print(f"🔌 Power Connected: {'Yes' if battery_info['power_plugged'] else 'No'}")
        print(f"⚡ Charging Speed: {battery_info.get('charging_speed', 'Unknown')}")
    else:
        print("❌ Could not get battery information")
    
    print(f"🎯 Notification will show: {notification.current_battery_percent}%")
    print(f"✅ Battery widget level: {notification.battery_widget.battery_level}%")
    
    # Show notification
    print("\n⚡ Showing notification with correct percentage...")
    notification.show_notification()
    
    # Auto-exit after 6 seconds
    QtCore.QTimer.singleShot(6000, app.quit)
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
