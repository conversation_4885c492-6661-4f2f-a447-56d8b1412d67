from PySide6 import QtWidgets, QtCore, QtGui
import sys
import os
import psutil
from PySide6.QtGui import QPixmap, QIcon
from PySide6.QtCore import QSize, Qt

class BatteryWidget(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()

        self.init_ui()

    def init_ui(self):
        self.setWindowFlags(
            QtCore.Qt.FramelessWindowHint
            | QtCore.Qt.Tool
        )
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.resize(200, 100) # Default size for battery widget

        # Load the custom font
        font_path = os.path.join(os.path.dirname(__file__), "Anurati-Regular.otf")
        if os.path.exists(font_path):
            font_id = QtGui.QFontDatabase.addApplicationFont(font_path)
            families = QtGui.QFontDatabase.applicationFontFamilies(font_id)
            self.font_family = families[0] if families else "Segoe UI"
        else:
            self.font_family = "Segoe UI"
            print(f"Warning: Font file not found at {font_path}. Using default font.")

        self.main_layout = QtWidgets.QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(5)
        self.main_layout.addStretch()

        # Layout for icon and percentage
        self.info_layout = QtWidgets.QHBoxLayout()
        self.info_layout.setContentsMargins(0, 0, 0, 0)
        self.info_layout.setSpacing(10)
        self.info_layout.addStretch()

        # Label to display battery icon
        self.icon_label = QtWidgets.QLabel("-") # Placeholder text
        self.icon_label.setAlignment(QtCore.Qt.AlignCenter)
        self.info_layout.addWidget(self.icon_label)

        # Label to display battery percentage
        self.battery_label = QtWidgets.QLabel("Loading...")
        self.battery_label.setAlignment(QtCore.Qt.AlignCenter)
        self.info_layout.addWidget(self.battery_label)

        self.info_layout.addStretch()

        self.main_layout.addLayout(self.info_layout)
        self.main_layout.addStretch()

        self.update_styles() # Apply initial styles
        self.show()

        # Timer to update battery status
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.update_battery_status)
        self.timer.start(5000) # Update every 5 seconds (adjust as needed)

        # Dragging functionality (copied from weather_widget)
        self.dragging = False
        self.drag_offset = QtCore.QPoint()

    def update_styles(self):
        # Apply styles using the loaded font
        battery_font = QtGui.QFont(self.font_family)
        battery_font.setPointSize(36)
        self.battery_label.setFont(battery_font)

        # Placeholder color style (can make this configurable later)
        self.battery_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 230); /* White with some transparency */
                background: transparent;
            }
        """)

        # Style for the icon label (optional, can adjust size/color if needed)
        self.icon_label.setStyleSheet("""
            QLabel {
                background: transparent; /* Removed temporary background */
            }
        """)

    def update_battery_status(self):
        try:
            battery = psutil.sensors_battery()
            if battery:
                percent = battery.percent
                is_charging = battery.power_plugged

                self.battery_label.setText(f"{percent}%")

                # Determine which icon to show
                icon_path = os.path.join(os.path.dirname(__file__), "images")
                print(f"Looking for icons in: {icon_path}") # Debug print
                current_icon = None

                if is_charging:
                    current_icon = os.path.join(icon_path, "battery-bolt.svg")
                elif percent > 80:
                    current_icon = os.path.join(icon_path, "battery-full.svg")
                elif percent > 40:
                    current_icon = os.path.join(icon_path, "battery-mid.svg")
                elif percent > 10:
                    current_icon = os.path.join(icon_path, "battery-low.svg")
                else:
                    current_icon = os.path.join(icon_path, "battery-empty.svg")

                print(f"Selected icon path: {current_icon}") # Debug print

                if current_icon and os.path.exists(current_icon):
                    print(f"Icon file exists: {current_icon}") # Debug print
                    pixmap = QPixmap(current_icon)
                    if pixmap.isNull():
                        print("Error loading pixmap. It is null.") # Debug print
                        self.icon_label.setText("Load Error") # Indicate loading failure
                    else:
                        # Scale the pixmap to a suitable size (adjust as needed)
                        scaled_pixmap = pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        self.icon_label.setPixmap(scaled_pixmap)
                        print("Pixmap scaled and set successfully.") # Debug print
                else:
                    print(f"Icon file not found: {current_icon}") # Debug print
                    self.icon_label.setText("-") # Fallback text if icon not found

            else:
                self.battery_label.setText("N/A") # Handle case where battery info is not available
                self.icon_label.setText("-")
        except Exception as e:
            self.battery_label.setText("Error")
            self.icon_label.setText("!")
            print(f"Error fetching battery status: {e}")

    # Add mouse events for dragging
    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.dragging = True
            self.drag_offset = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        if self.dragging and event.buttons() == QtCore.Qt.LeftButton:
            new_pos = event.globalPosition().toPoint() - self.drag_offset
            self.move(new_pos)
            event.accept()

    def mouseReleaseEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.dragging = False
            event.accept()


if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    widget = BatteryWidget()
    sys.exit(app.exec()) 