#!/usr/bin/env python3
"""
Dynamic Power Connection Notification System
A sleek, physics-based notification component for laptop charging status.
"""

from PySide6 import QtWidgets, QtCore, QtGui
import sys
import os
from power_notification_widget import PowerNotificationWidget


class PowerNotificationApp(QtWidgets.QApplication):
    """Main application for the power notification system."""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Set application properties
        self.setApplicationName("Power Notification")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("Wigfy")
        
        # Create notification widget
        self.notification_widget = PowerNotificationWidget()
        
        # Create system tray for testing (optional)
        self.create_system_tray()
        
        # Start power monitoring
        self.notification_widget.start_monitoring()
        
    def create_system_tray(self):
        """Create system tray icon for testing and control."""
        if not QtWidgets.QSystemTrayIcon.isSystemTrayAvailable():
            print("System tray is not available on this system.")
            return
            
        # Create tray icon
        self.tray_icon = QtWidgets.QSystemTrayIcon(self)
        
        # Set icon (use power icon or default)
        icon_path = os.path.join(os.path.dirname(__file__), "images", "battery-bolt.svg")
        if os.path.exists(icon_path):
            self.tray_icon.setIcon(QtGui.QIcon(icon_path))
        else:
            # Use default icon
            style = self.style()
            icon = style.standardIcon(QtWidgets.QStyle.StandardPixmap.SP_ComputerIcon)
            self.tray_icon.setIcon(icon)
            
        # Create context menu
        tray_menu = QtWidgets.QMenu()
        
        # Test notification action
        test_action = QtGui.QAction("Test Notification", self)
        test_action.triggered.connect(self.notification_widget.trigger_test_notification)
        tray_menu.addAction(test_action)

        tray_menu.addSeparator()

        # Show current battery status
        status_action = QtGui.QAction("Battery Status", self)
        status_action.triggered.connect(self.show_battery_status)
        tray_menu.addAction(status_action)

        tray_menu.addSeparator()

        # Quit action
        quit_action = QtGui.QAction("Quit", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.setToolTip("Power Notification System")
        
        # Show tray icon
        self.tray_icon.show()
        
        # Connect tray icon activation
        self.tray_icon.activated.connect(self.on_tray_activated)
        
    def on_tray_activated(self, reason):
        """Handle tray icon activation."""
        if reason == QtWidgets.QSystemTrayIcon.ActivationReason.DoubleClick:
            self.notification_widget.trigger_test_notification()
            
    def show_battery_status(self):
        """Show current battery status in a message box."""
        battery_info = self.notification_widget.power_monitor.get_current_battery_info()
        
        if battery_info:
            status = "Connected" if battery_info['power_plugged'] else "Disconnected"
            message = f"Battery: {battery_info['percent']}%\nPower: {status}"
        else:
            message = "Battery information not available (desktop system?)"
            
        QtWidgets.QMessageBox.information(
            None,
            "Battery Status",
            message
        )
        
    def quit_application(self):
        """Quit the application cleanly."""
        self.notification_widget.stop_monitoring()
        self.quit()


def main():
    """Main entry point."""
    # Create application
    app = PowerNotificationApp(sys.argv)
    
    # Show startup message
    print("Power Notification System started!")
    print("- Monitoring power connection changes")
    print("- Right-click system tray icon for options")
    print("- Double-click tray icon to test notification")
    print("- Press Ctrl+C to quit")
    
    # Run application
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\nShutting down...")
        app.quit_application()


if __name__ == "__main__":
    main()
