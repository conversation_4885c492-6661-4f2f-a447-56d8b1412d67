#!/usr/bin/env python3
"""
Quick test for the Power Notification Widget
Shows a notification for 5 seconds then exits.
"""

from PySide6 import QtWidgets, QtCore
import sys
from power_notification_widget import PowerNotificationWidget


def main():
    """Quick test of the notification."""
    app = QtWidgets.QApplication(sys.argv)
    
    # Create notification widget
    notification = PowerNotificationWidget()
    
    print("Showing power notification...")
    
    # Show notification
    notification.show_notification()
    
    # Auto-exit after 6 seconds
    QtCore.QTimer.singleShot(6000, app.quit)
    
    # Run app
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
