{"design_system": {"dimensions": {"width": 280, "height": 44, "border_radius": 22, "inner_padding": 16, "spacing": 12}, "macos_style": {"background": {"base": "rgba(28, 28, 30, 0.98)", "blur_effect": true, "backdrop_blur": 30}, "border": {"color": "rgba(255, 255, 255, 0.08)", "width": 0.5}, "shadows": [{"blur": 20, "offset": [0, 8], "color": "rgba(0, 0, 0, 0.3)", "spread": 0}, {"blur": 40, "offset": [0, 16], "color": "rgba(0, 0, 0, 0.15)", "spread": 0}]}, "typography": {"charging_text": {"font_family": "-apple-system, BlinkMacSystemFont, 'SF Pro Text', system-ui", "font_size": 15, "font_weight": 590, "color": "#FFFFFF", "letter_spacing": -0.1}, "percentage_text": {"font_family": "-apple-system, BlinkMacSystemFont, 'SF Pro Text', system-ui", "font_size": 15, "font_weight": 590, "color": "#FFFFFF", "letter_spacing": -0.1}}, "battery_indicator": {"width": 32, "height": 18, "border_radius": 3, "border_color": "rgba(255, 255, 255, 0.3)", "border_width": 1, "fill_colors": {"charging": "#30D158", "high": "#30D158", "medium": "#FFD60A", "low": "#FF453A", "critical": "#FF453A"}, "terminal": {"width": 2, "height": 8, "border_radius": 1, "color": "rgba(255, 255, 255, 0.3)"}}}, "premium_animations": {"entrance_sequence": {"total_duration": 1200, "phases": [{"name": "initial_blur_in", "duration": 150, "delay": 0, "easing": "ease_out_cubic", "properties": {"backdrop_blur": "0 to 30", "opacity": "0 to 0.3"}}, {"name": "slide_down", "duration": 600, "delay": 100, "easing": "spring_smooth", "properties": {"y_position": "-60 to final", "opacity": "0.3 to 1.0", "scale": "0.92 to 1.0"}}, {"name": "content_reveal", "duration": 400, "delay": 300, "easing": "ease_out_quart", "properties": {"text_opacity": "0 to 1", "battery_scale": "0.7 to 1.0", "battery_fill": "0% to actual%"}}, {"name": "glow_activation", "duration": 300, "delay": 600, "easing": "ease_out_sine", "properties": {"shadow_intensity": "0.5 to 1.0", "border_glow": "0 to 1"}}]}, "battery_fill_animation": {"duration": 800, "easing": "ease_out_cubic", "delay": 400, "properties": {"fill_width": "0% to target%", "glow_intensity": "0 to 1"}}, "micro_animations": {"subtle_pulse": {"duration": 3000, "easing": "ease_in_out_sine", "loop": true, "properties": {"shadow_opacity": "0.8 to 1.0 to 0.8", "border_glow": "0.6 to 1.0 to 0.6"}}, "battery_breathing": {"duration": 2500, "easing": "ease_in_out_sine", "loop": true, "properties": {"battery_glow": "0.8 to 1.2 to 0.8"}}}, "interaction_feedback": {"hover_enter": {"duration": 200, "easing": "ease_out_cubic", "properties": {"scale": "1.0 to 1.02", "shadow_intensity": "1.0 to 1.3", "border_brightness": "1.0 to 1.5"}}, "hover_exit": {"duration": 300, "easing": "ease_out_cubic", "properties": {"scale": "1.02 to 1.0", "shadow_intensity": "1.3 to 1.0", "border_brightness": "1.5 to 1.0"}}, "click_feedback": {"duration": 120, "easing": "ease_out_cubic", "properties": {"scale": "1.0 to 0.97 to 1.0", "opacity": "1.0 to 0.9 to 1.0"}}}, "exit_sequence": {"total_duration": 400, "phases": [{"name": "scale_fade", "duration": 400, "easing": "ease_in_cubic", "properties": {"scale": "1.0 to 0.88", "opacity": "1.0 to 0", "y_offset": "0 to -20", "blur": "0 to 8"}}]}}, "behavior": {"display_duration": 4000, "position": {"x": "center", "y": 20}, "auto_dismiss": true, "click_to_dismiss": true, "swipe_to_dismiss": true}, "contextual_content": {"charging_states": {"fast_charging": {"text": "Fast Charging", "battery_color": "#30D158", "glow_intensity": 1.2}, "charging": {"text": "Charging", "battery_color": "#30D158", "glow_intensity": 1.0}, "trickle_charging": {"text": "Charging", "battery_color": "#30D158", "glow_intensity": 0.8}, "battery_full": {"text": "Charged", "battery_color": "#30D158", "glow_intensity": 1.0}}}}