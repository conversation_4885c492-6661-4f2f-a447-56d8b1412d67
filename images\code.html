<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxe Smart Home Dashboard - Fixed & Polished</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* --- Light Mode Palette --- */
            --bg-gradient-start-light: #F0F7FF; --bg-gradient-end-light: #FAFCFF;
            --primary-accent-light: #6C9FF0; --primary-accent-darker-light: #558AD5; --primary-accent-rgb-light: 108, 159, 240;
            --text-primary-light: #2E3D4D; --text-secondary-light: #708294; --text-value-light: #263343;
            --text-good-status-light: #27AE60; --text-alert-status-light: #E74C3C; --text-warn-status-light: #F39C12;
            --graph-line-color-light: #A0B8D3; --graph-fill-start-rgba-light: rgba(160, 184, 211, 0.12); --graph-fill-end-rgba-light: rgba(160, 184, 211, 0.0); --graph-tick-color-light: rgba(160, 184, 211, 0.6);
            --neumorphic-base-light: #F2F7FC; --highlight-color-light: rgba(255, 255, 255, 0.95); --shadow-color-light: rgba(190, 205, 225, 0.6);
            --neumorphic-pressed-bg-light: linear-gradient(145deg, #e4eaf4, #fcfdff);
            --neumorphic-toggle-track-light: linear-gradient(145deg, #dde5ed, #f5f9fe);
            --glass-bg-light: rgba(247, 250, 254, 0.8); --glass-border-light: rgba(255, 255, 255, 0.6); --glass-shadow-light: 0 8px 30px rgba(100, 130, 180, 0.15);
            --sidebar-icon-active-bg-light: var(--primary-accent-light); --sidebar-icon-active-shadow-light: 0 0 0 2.5px rgba(var(--primary-accent-rgb-light),0.35);
            --sidebar-icon-inactive-bg-light: var(--neumorphic-base-light);
            --radial-dial-bg-light: var(--neumorphic-base-light); --radial-dial-track-light: #D8E2EF; --radial-dial-handle-light: #FFFFFF; --radial-dial-handle-shadow-light: rgba(0,0,0,0.15);
            --house-base-fill-light: rgba(245, 250, 255, 0.7); --house-base-stroke-light: rgba(225, 235, 250, 0.9);
            --house-right-wall-fill-light: var(--wall-grad-stop1-color-light);
            --house-dormer-left-fill-light: var(--wall-grad-stop1-color-light);
            --house-dormer-right-fill-light: var(--wall-grad-stop2-color-light);


            /* --- Dark Mode Palette --- */
            --bg-gradient-start-dark: #171E2A; --bg-gradient-end-dark: #263142; 
            --primary-accent-dark: #70A5F5; --primary-accent-darker-dark: #88B8FF; --primary-accent-rgb-dark: 112, 165, 245;
            --text-primary-dark: #E0E7F0; --text-secondary-dark: #9AA8BA; --text-value-dark: #F5F8FB;
            --text-good-status-dark: #58C68A; --text-alert-status-dark: #F87878; --text-warn-status-dark: #F7B76A;
            --graph-line-color-dark: #6A8AAE; --graph-fill-start-rgba-dark: rgba(106, 138, 174, 0.11); --graph-fill-end-rgba-dark: rgba(106, 138, 174, 0.0); --graph-tick-color-dark: rgba(106, 138, 174, 0.4);
            --neumorphic-base-dark: #242E3E; 
            --highlight-color-dark: rgba(50, 62, 80, 0.7); 
            --shadow-color-dark: rgba(18, 23, 32, 0.6);
            --neumorphic-pressed-bg-dark: linear-gradient(145deg, #202835, #283242);
            --neumorphic-toggle-track-dark: linear-gradient(145deg, #1e2531, #262f3d);
            --glass-bg-dark: rgba(36, 46, 62, 0.78); 
            --glass-border-dark: rgba(60, 72, 90, 0.6); --glass-shadow-dark: 0 8px 30px rgba(0, 0, 0, 0.3);
            --sidebar-icon-active-bg-dark: var(--primary-accent-dark); --sidebar-icon-active-shadow-dark: 0 0 0 2.5px rgba(var(--primary-accent-rgb-dark),0.25);
            --sidebar-icon-inactive-bg-dark: var(--neumorphic-base-dark);
            --radial-dial-bg-dark: var(--neumorphic-base-dark); --radial-dial-track-dark: #303C4F; --radial-dial-handle-dark: #4A5A72; --radial-dial-handle-shadow-dark: rgba(0,0,0,0.25);
            --house-base-fill-dark: rgba(45,55,72,0.5); --house-base-stroke-dark: rgba(60,72,90,0.7);
            --house-right-wall-fill-dark: var(--wall-grad-stop2-color-dark);
            --house-dormer-left-fill-dark: var(--wall-grad-stop2-color-dark);
            --house-dormer-right-fill-dark: var(--wall-grad-stop1-color-dark);

            /* Universal Vars */
            --shadow-distance: 5px; --shadow-blur: 16px; 
            --border-radius-xl: 30px; --border-radius-l: 24px; --border-radius-m: 18px; --border-radius-s: 14px; 
            --font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            --transition-cubic-elegant: cubic-bezier(0.645, 0.045, 0.355, 1); 
            --transition-duration-normal: 0.4s; --transition-duration-fast: 0.3s; --transition-duration-content-swap: 0.35s;

             /* Variables for SVG styles (used by inline CSS inside SVG defs) */
            --primary-accent-win-glass: #C5E3FF; --primary-accent-win-frame-stroke: #AFC3DD; --primary-accent-win-frame-fill: #E8F3FF;
            --primary-accent-door-fill: #8097B5; --primary-accent-roof-stroke: #5E79A0; --primary-accent-wall-stroke: #C4D0E2;
            --roof-grad-stop1-color: #7392C2; --roof-grad-stop2-color: #8BA9DB;
            --wall-grad-stop1-color: #F4F8FC; --wall-grad-stop2-color: #E6EDF5;
            --house-handle-fill: #6A829E; --house-line-stroke: #AFC3DD;

            /* Initial mode mapping */
            --bg-gradient-start: var(--bg-gradient-start-light); --bg-gradient-end: var(--bg-gradient-end-light);
            --primary-accent: var(--primary-accent-light); --primary-accent-darker: var(--primary-accent-darker-light); --primary-accent-rgb: var(--primary-accent-rgb-light);
            --text-primary: var(--text-primary-light); --text-secondary: var(--text-secondary-light); --text-value: var(--text-value-light);
            --text-good-status: var(--text-good-status-light); --text-alert-status: var(--text-alert-status-light); --text-warn-status: var(--text-warn-status-light);
            --graph-line-color: var(--graph-line-color-light); --graph-fill-start-rgba: var(--graph-fill-start-rgba-light); --graph-fill-end-rgba: var(--graph-fill-end-rgba-light); --graph-tick-color: var(--graph-tick-color-light);
            --neumorphic-base: var(--neumorphic-base-light); --highlight-color: var(--highlight-color-light); --shadow-color: var(--shadow-color-light);
            --neumorphic-pressed-bg: var(--neumorphic-pressed-bg-light); --neumorphic-toggle-track: var(--neumorphic-toggle-track-light);
            --glass-bg: var(--glass-bg-light); --glass-border: var(--glass-border-light); --glass-shadow: var(--glass-shadow-light);
            --sidebar-icon-active-bg: var(--sidebar-icon-active-bg-light); --sidebar-icon-active-shadow: var(--sidebar-icon-active-shadow-light);
            --sidebar-icon-inactive-bg: var(--sidebar-icon-inactive-bg-light);
            --radial-dial-bg: var(--radial-dial-bg-light); --radial-dial-track: var(--radial-dial-track-light); --radial-dial-handle: var(--radial-dial-handle-light); --radial-dial-handle-shadow: var(--radial-dial-handle-shadow-light);
            --house-base-fill: var(--house-base-fill-light); --house-base-stroke: var(--house-base-stroke-light);
            --house-right-wall-fill: var(--house-right-wall-fill-light);
            --house-dormer-left-fill: var(--house-dormer-left-fill-light); --house-dormer-right-fill: var(--house-dormer-right-fill-light);
        }

        body.dark-mode {
            --bg-gradient-start: var(--bg-gradient-start-dark); --bg-gradient-end: var(--bg-gradient-end-dark);
            --primary-accent: var(--primary-accent-dark); --primary-accent-darker: var(--primary-accent-darker-dark); --primary-accent-rgb: var(--primary-accent-rgb-dark);
            --text-primary: var(--text-primary-dark); --text-secondary: var(--text-secondary-dark); --text-value: var(--text-value-dark);
            --text-good-status: var(--text-good-status-dark); --text-alert-status: var(--text-alert-status-dark); --text-warn-status: var(--text-warn-status-dark);
            --graph-line-color: var(--graph-line-color-dark); --graph-fill-start-rgba: var(--graph-fill-start-rgba-dark); --graph-fill-end-rgba: var(--graph-fill-end-rgba-dark); --graph-tick-color: var(--graph-tick-color-dark);
            --neumorphic-base: var(--neumorphic-base-dark); --highlight-color: var(--highlight-color-dark); --shadow-color: var(--shadow-color-dark);
            --neumorphic-pressed-bg: var(--neumorphic-pressed-bg-dark); --neumorphic-toggle-track: var(--neumorphic-toggle-track-dark);
            --glass-bg: var(--glass-bg-dark); --glass-border: var(--glass-border-dark); --glass-shadow: var(--glass-shadow-dark);
            --sidebar-icon-active-bg: var(--sidebar-icon-active-bg-dark); --sidebar-icon-active-shadow: var(--sidebar-icon-active-shadow-dark);
            --sidebar-icon-inactive-bg: var(--sidebar-icon-inactive-bg-dark);
            --radial-dial-bg: var(--radial-dial-bg-dark); --radial-dial-track: var(--radial-dial-track-dark); --radial-dial-handle: var(--radial-dial-handle-dark); --radial-dial-handle-shadow: var(--radial-dial-handle-shadow-dark);
            --house-base-fill: var(--house-base-fill-dark); --house-base-stroke: var(--house-base-stroke-dark);
            --house-right-wall-fill: var(--house-right-wall-fill-dark);
            --house-dormer-left-fill: var(--house-dormer-left-fill-dark); --house-dormer-right-fill: var(--house-dormer-right-fill-dark);

            /* Dark Mode SVG Vars */
            --primary-accent-win-glass: #5A7DAB; --primary-accent-win-frame-stroke: #4A658A; --primary-accent-win-frame-fill: #3A5070;
            --primary-accent-door-fill: #607890; --primary-accent-roof-stroke: #405570; --primary-accent-wall-stroke: #506580;
            --roof-grad-stop1-color: #4A658A; --roof-grad-stop2-color: #5A7DAB;
            --wall-grad-stop1-color: #2D3748; --wall-grad-stop2-color: #3A475A;
            --house-handle-fill: #506580; --house-line-stroke: #4A658A;
        }

        /* Base & Global Styles (largely unchanged from previous polished version) */
        * { margin: 0; padding: 0; box-sizing: border-box; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
        body { font-family: var(--font-family); background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end)); min-height: 100vh; display: flex; justify-content: center; align-items: center; color: var(--text-primary); padding: 15px; overflow: hidden; transition: background var(--transition-duration-normal) var(--transition-cubic-elegant), color var(--transition-duration-normal) var(--transition-cubic-elegant); }
        .material-symbols-outlined { font-variation-settings: 'FILL' 0, 'wght' 300, 'GRAD' 0, 'opsz' 24; font-size: 24px; display: inline-flex; align-items: center; justify-content: center; line-height: 1; transition: color var(--transition-duration-fast) var(--transition-cubic-elegant); user-select: none;}
        .dashboard-container { width: 100%; max-width: 1500px; height: calc(100vh - 30px); max-height: 920px; background-color: rgba(var(--primary-accent-rgb), 0.02); border-radius: var(--border-radius-xl); box-shadow: 0 15px 50px rgba(140, 170, 210, 0.18); display: grid; grid-template-columns: 95px 1fr 360px; grid-template-rows: 75px 1fr; grid-template-areas: "sidebar header header" "sidebar main right-panel"; gap: 22px; padding: 22px; opacity: 0; animation: fadeInDashboard var(--transition-duration-normal) var(--transition-cubic-elegant) 0.2s forwards; }
        body.dark-mode .dashboard-container { background-color: rgba(var(--primary-accent-rgb), 0.03); box-shadow: 0 15px 50px rgba(0,0,0, 0.22); }
        @keyframes fadeInDashboard { to { opacity: 1; } }

        .neumorphic-element { background: var(--neumorphic-base); transition-property: box-shadow, transform, background; transition-duration: var(--transition-duration-normal); transition-timing-function: var(--transition-cubic-elegant); }
        .neumorphic-outset { border-radius: var(--border-radius-m); box-shadow: calc(var(--shadow-distance) * -1) calc(var(--shadow-distance) * -1) var(--shadow-blur) var(--highlight-color), var(--shadow-distance) var(--shadow-distance) var(--shadow-blur) var(--shadow-color); }
        .neumorphic-outset:hover { box-shadow: calc(var(--shadow-distance) * -0.7) calc(var(--shadow-distance) * -0.7) calc(var(--shadow-blur) * 0.8) var(--highlight-color), calc(var(--shadow-distance) * 0.7) calc(var(--shadow-distance) * 0.7) calc(var(--shadow-blur) * 0.8) var(--shadow-color); transform: translateY(-2px) scale(1.01); }
        .neumorphic-pressed { border-radius: var(--border-radius-m); background: var(--neumorphic-pressed-bg); box-shadow: inset calc(var(--shadow-distance) * 0.8) calc(var(--shadow-distance) * 0.8) calc(var(--shadow-blur) * 0.9) var(--shadow-color), inset calc(var(--shadow-distance) * -0.8) calc(var(--shadow-distance) * -0.8) calc(var(--shadow-blur) * 0.9) var(--highlight-color); transform: translateY(1px) scale(0.99); }
        
        .glass-panel { background: var(--glass-bg); backdrop-filter: blur(var(--glass-blur)); -webkit-backdrop-filter: blur(var(--glass-blur)); border-radius: var(--border-radius-l); border: 1px solid var(--glass-border); box-shadow: var(--glass-shadow); padding: 22px; transition: transform var(--transition-duration-normal) var(--transition-cubic-elegant), box-shadow var(--transition-duration-normal) var(--transition-cubic-elegant), background var(--transition-duration-normal) var(--transition-cubic-elegant), border var(--transition-duration-normal) var(--transition-cubic-elegant); }
        .glass-panel:hover { transform: translateY(-4px) perspective(1200px) rotateX(0.7deg) rotateY(-0.7deg); box-shadow: 0 14px 42px rgba(100, 130, 180, 0.22); }
        body.dark-mode .glass-panel:hover { box-shadow: 0 14px 42px rgba(0,0,0, 0.38); }

        /* Header Styles (Includes Theme Switcher styling) */
        .header { grid-area: header; display: flex; justify-content: space-between; align-items: center; padding: 0 20px; opacity: 0; animation: fadeInElement var(--transition-duration-normal) var(--transition-cubic-elegant) 0.3s forwards; }
        .header h1 { font-size: 30px; font-weight: 600; color: var(--text-primary); letter-spacing: -0.7px;}
        .header-actions { display: flex; align-items: center; gap: 18px; }
        .header-actions .icon-button { width: 46px; height: 46px; display: flex; align-items: center; justify-content: center; cursor: pointer; }
        .header-actions .icon-button .material-symbols-outlined { font-size: 22px; color: var(--text-secondary); }
        .header-actions .icon-button:hover .material-symbols-outlined { color: var(--primary-accent); transform: scale(1.08) rotate(-5deg); }
        #theme-switcher .material-symbols-outlined { transition: color var(--transition-duration-fast) ease, transform var(--transition-duration-normal) var(--transition-cubic-elegant) !important; }
        #theme-switcher:hover .material-symbols-outlined { transform: scale(1.15) rotate(30deg) !important;}
        .user-avatar { width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center; padding: 3px; }
        .user-avatar-inner { width: 100%; height: 100%; border-radius: 50%; background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='50' cy='50' r='45' fill='%23DDE5F0'/%3E%3Cpath d='M50 60 C 40 60, 32 68, 32 78 V 85 H 68 V 78 C 68 68, 60 60, 50 60 Z M 50 28 C 43 28, 38 33, 38 40 S 43 52, 50 52, 62 47, 62 40, 57 28, 50 28 Z' fill='%23FBFDFF'/%3E%3C/svg%3E"); background-size: cover; display: flex; align-items: center; justify-content: center; }
        body.dark-mode .user-avatar-inner { background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='50' cy='50' r='45' fill='%23404C60'/%3E%3Cpath d='M50 60 C 40 60, 32 68, 32 78 V 85 H 68 V 78 C 68 68, 60 60, 50 60 Z M 50 28 C 43 28, 38 33, 38 40 S 43 52, 50 52, 62 47, 62 40, 57 28, 50 28 Z' fill='%239AA8BA'/%3E%3C/svg%3E");}

        /* Sidebar Styles */
        .sidebar { grid-area: sidebar; display: flex; flex-direction: column; align-items: center; padding-top: 30px; gap: 24px; opacity: 0; animation: fadeInElement var(--transition-duration-normal) var(--transition-cubic-elegant) 0.4s forwards; }
        .sidebar-icon { width: 52px; height: 52px; display: flex; align-items: center; justify-content: center; cursor: pointer; border-radius: var(--border-radius-s); transition-property: background, box-shadow, transform; transition-duration: var(--transition-duration-fast); transition-timing-function: var(--transition-cubic-elegant); background: var(--sidebar-icon-inactive-bg); }
        .sidebar-icon .material-symbols-outlined { font-size: 24px; color: var(--text-secondary); }
        .sidebar-icon:not(.active) { box-shadow: -4px -4px 10px var(--highlight-color), 4px  4px 10px var(--shadow-color); }
        .sidebar-icon:not(.active):hover { transform: scale(1.05) translateY(-2px); background: var(--neumorphic-base); box-shadow: -3px -3px 8px var(--highlight-color), 3px  3px 8px var(--shadow-color); }
        .sidebar-icon:not(.active):hover .material-symbols-outlined { color: var(--primary-accent); transform: scale(1.05); }
        .sidebar-icon.active { background: var(--sidebar-icon-active-bg); box-shadow: inset 3px 3px 6px rgba(0,0,0,0.12), inset -2px -2px 5px rgba(255,255,255,0.35), var(--sidebar-icon-active-shadow); transform: scale(0.95); }
        body.dark-mode .sidebar-icon.active {box-shadow: inset 3px 3px 6px rgba(0,0,0,0.25), inset -2px -2px 5px rgba(255,255,255,0.1), var(--sidebar-icon-active-shadow); }
        .sidebar-icon.active .material-symbols-outlined { color: #fff; transform: scale(0.95);}

        /* Main Content & Tabs Styles (largely unchanged) */
        .main-content { grid-area: main; overflow: hidden; display: flex; position: relative; opacity: 0; animation: fadeInElement var(--transition-duration-normal) var(--transition-cubic-elegant) 0.5s forwards; }
        @keyframes fadeInElement { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); }}
        .tab-content { width: 100%; height: 100%; overflow-y: auto; padding: 8px; position: absolute; opacity: 0; visibility: hidden; transform: scale(0.98) translateY(10px); transition: opacity var(--transition-duration-content-swap) var(--transition-cubic-elegant), visibility 0s var(--transition-duration-content-swap) var(--transition-cubic-elegant), transform var(--transition-duration-content-swap) var(--transition-cubic-elegant); }
        .tab-content.active { opacity: 1; visibility: visible; transform: scale(1) translateY(0); transition-delay: 0s; }
        .tab-content::-webkit-scrollbar { width: 7px; } .tab-content::-webkit-scrollbar-track { background: rgba(var(--primary-accent-rgb), 0.05); border-radius: 10px; } .tab-content::-webkit-scrollbar-thumb { background: var(--primary-accent); border-radius: 10px; opacity: 0.4; transition: opacity var(--transition-duration-fast) ease;} .tab-content:hover::-webkit-scrollbar-thumb { opacity: 0.7; } .tab-content::-webkit-scrollbar-thumb:hover { opacity: 1; }
        .main-content-card { height: 100%; padding: 28px 35px; border-radius: var(--border-radius-l); background: var(--neumorphic-base); box-shadow: -10px -10px 20px var(--highlight-color), 10px  10px 20px var(--shadow-color); transition: box-shadow var(--transition-duration-normal) var(--transition-cubic-elegant), background var(--transition-duration-normal) var(--transition-cubic-elegant); }
        .main-content-card:hover { box-shadow: -8px -8px 18px var(--highlight-color), 8px  8px 18px var(--shadow-color); }
        .main-content-card h2 { font-size: 28px; font-weight: 600; margin-bottom: 28px; color: var(--text-value); letter-spacing: -0.5px;}
        .main-content-card h3 { font-size: 22px; font-weight: 500; margin-bottom: 18px; color: var(--text-primary);}
        
        /* Common Interactive Element Styling */
        .room-info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(145px, 1fr)); gap: 28px; margin-bottom: 28px;}
        .info-item .label { font-size: 14px; color: var(--text-secondary); margin-bottom: 8px; font-weight: 400; }
        .info-item .value { font-size: 30px; font-weight: 600; color: var(--text-value); letter-spacing: -0.2px;}
        .info-item .value.air-good { color: var(--text-good-status); }
        .graph-container { height: 105px; margin-bottom: 28px; border-radius: var(--border-radius-m); overflow: hidden; }
        .lights-control-section { display: flex; align-items: center; justify-content: space-between; padding: 12px 0; margin-bottom: 10px;}
        .lights-control-section .label { font-size: 19px; font-weight: 500; color: var(--text-primary); }
        .toggle-switch-outer { width: 64px; height: 32px; border-radius: 16px; padding: 3px; display: flex; align-items: center; background: var(--neumorphic-toggle-track); box-shadow: inset 2.5px 2.5px 5px var(--shadow-color), inset -2.5px -2.5px 5px var(--highlight-color); cursor: pointer; transition: background var(--transition-duration-fast) var(--transition-cubic-elegant), box-shadow var(--transition-duration-fast) var(--transition-cubic-elegant); }
        .toggle-switch-outer.on { background: var(--primary-accent); box-shadow: inset 2px 2px 4px rgba(0,0,0,0.15), inset -1px -1px 3px rgba(255,255,255,0.2); }
        body.dark-mode .toggle-switch-outer.on { box-shadow: inset 2px 2px 4px rgba(0,0,0,0.3), inset -1px -1px 3px rgba(255,255,255,0.05); }
        .toggle-switch-nub { width: 26px; height: 26px; background-color: #fff; border-radius: 50%; box-shadow: 0px 3px 6px rgba(0,0,0,0.15), 0 1px 2px rgba(0,0,0,0.1); transition: transform var(--transition-duration-normal) var(--transition-cubic-elegant); margin-left: 0px; }
        body.dark-mode .toggle-switch-nub { background-color: #CCD2DA; } 
        .toggle-switch-outer.on .toggle-switch-nub { transform: translateX(29px); }
        .lights-graph-container { height: 105px; margin-top: 10px; border-radius: var(--border-radius-m); overflow: hidden; }

        /* Lights Tab Content Styles */
        .lights-tab-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 25px; margin-bottom: 30px;}
        .light-control-card { padding: 22px; border-radius: var(--border-radius-m); display: flex; flex-direction: column; gap: 15px;}
        .light-control-card-header { display: flex; align-items: center; justify-content: space-between;}
        .light-control-card-header .material-symbols-outlined { font-size: 32px; color: var(--primary-accent); }
        .light-control-card .label { font-size: 18px; font-weight: 500; color: var(--text-primary);}
        .light-control-card .room-name {font-size: 13px; color: var(--text-secondary); margin-top: -12px; margin-bottom: 8px;}

        .scenes-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; }
        .scene-card { padding: 20px; text-align: center; border-radius: var(--border-radius-m); display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 10px; cursor:pointer; min-height: 130px;} /* Increased min-height */
        .scene-card .material-symbols-outlined { font-size: 34px; color: var(--primary-accent); margin-bottom: 8px; transition: transform var(--transition-duration-fast) var(--transition-cubic-elegant), color var(--transition-duration-fast) var(--transition-cubic-elegant);}
        .scene-card .label { font-size: 16px; font-weight: 500; color: var(--text-primary);}
        .scene-card:hover .material-symbols-outlined { transform: scale(1.1) translateY(-2px); }
        .scene-card.active { background: var(--primary-accent)!important; box-shadow: 0 6px 18px rgba(var(--primary-accent-rgb), 0.35) !important;} /* Stronger active shadow */
        .scene-card.active .material-symbols-outlined, .scene-card.active .label { color: #fff!important;}

        /* Temperature Tab - Radial Dial Specific Styles */
        .thermostat-container { display: flex; flex-direction: column; align-items: center; padding: 10px 0;}
        .radial-thermostat-wrapper { width: 240px; height: 240px; position: relative; user-select: none; margin-bottom: 25px; } /* Slightly larger */
        .dial-bg-neumorphic, .dial-svg-track, .dial-svg-progress, #dial-touch-target { position: absolute; top: 0; left: 0; width: 100%; height: 100%; border-radius: 50%; }
        .dial-bg-neumorphic { background: var(--radial-dial-bg); box-shadow: inset 7px 7px 14px var(--shadow-color), inset -7px -7px 14px var(--highlight-color); } /* More pronounced inset */
        .dial-svg-track, .dial-svg-progress { fill: none; stroke-linecap: round; transform: rotate(-90deg); transform-origin: center; } /* Rotate SVG to start at top */
        .dial-svg-track path { stroke: var(--radial-dial-track); stroke-width: 22; }
        .dial-svg-progress path { stroke: var(--primary-accent); stroke-width: 22; transition: stroke-dashoffset var(--transition-duration-fast) ease-out; }
        .dial-center-display { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; z-index: 2;}
        .dial-current-temp-value { font-size: 50px; font-weight: 600; color: var(--text-value); line-height: 1; }
        .dial-setpoint-label { font-size: 15px; color: var(--text-secondary); margin-top: 4px;}
        #dial-drag-handle { width: 30px; height: 30px; background-color: var(--radial-dial-handle); border-radius: 50%; position: absolute; z-index: 3; cursor: grab; box-shadow: 0 3px 7px var(--radial-dial-handle-shadow); display:flex; align-items:center; justify-content:center; transform-origin: center; } /* transform-origin for rotation if handle image used */
        #dial-drag-handle:active { cursor: grabbing; }
        #dial-touch-target { cursor: default; z-index: 2; /* No grab cursor on whole area unless over track */}
        .thermostat-controls { display: flex; justify-content: center; align-items: center; gap: 30px; margin-bottom: 25px; margin-top: 15px;}
        .thermostat-controls .control-button { width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; cursor: pointer; border-radius: 50%; }
        .thermostat-controls .control-button .material-symbols-outlined { font-size: 30px; color: var(--text-secondary);}
        .thermostat-modes { display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;}
        .mode-button { padding: 12px 22px; border-radius: var(--border-radius-m); font-weight: 500; cursor: pointer; display:flex; align-items:center; gap: 8px;}
        .mode-button .material-symbols-outlined {font-size: 20px;}
        .mode-button.active { background: var(--primary-accent)!important; color: #fff!important;}
        .mode-button.active .material-symbols-outlined {color: #fff!important;}

        /* Security Tab Styles (largely unchanged, ensures variable updates) */
         .security-status-and-actions { display: flex; align-items: center; justify-content: space-around; gap: 30px; margin-bottom: 30px; padding: 20px; background: rgba(var(--primary-accent-rgb), 0.03); border-radius: var(--border-radius-m); }
        .system-status-prominent .material-symbols-outlined {font-size: 48px; margin-bottom: 8px;}
        .system-status-text-large { font-size: 20px; font-weight: 600; margin-bottom: 5px;}
        .system-status-subtext { font-size: 14px; color: var(--text-secondary);}
        .security-main-actions { display: flex; flex-direction: column; gap: 15px;}
        .arm-disarm-button { padding: 16px 28px; border-radius: var(--border-radius-m); font-size: 17px; font-weight: 600; display: flex; align-items: center; justify-content: center; gap: 10px; cursor: pointer; text-align: center; min-width: 180px; transition: background-color var(--transition-duration-fast), transform var(--transition-duration-fast); }
        .arm-disarm-button.arm { background-color: var(--text-alert-status); color: white; } .arm-disarm-button.arm:hover { background-color: var(--text-alert-status-light); transform: translateY(-1px) scale(1.02); } body.dark-mode .arm-disarm-button.arm:hover {background-color: var(--text-alert-status-dark) !important; filter: brightness(1.1);}
        .arm-disarm-button.disarm { background-color: var(--text-good-status); color: white; } .arm-disarm-button.disarm:hover { background-color: var(--text-good-status-light); transform: translateY(-1px) scale(1.02); } body.dark-mode .arm-disarm-button.disarm:hover {background-color: var(--text-good-status-dark) !important; filter: brightness(1.1);}
        .arm-disarm-button .material-symbols-outlined { font-size: 22px;}
        .sensor-status-grid {display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 18px; margin-bottom: 25px;}
        .sensor-item {display: flex; align-items: center; gap:12px; padding: 12px 15px; border-radius: var(--border-radius-m); }
        .sensor-item .material-symbols-outlined {color: var(--primary-accent); font-size: 26px;}
        .sensor-item .label { font-size: 15px; color: var(--text-primary); font-weight: 500;}
        .sensor-item .status-dot {width: 10px; height: 10px; border-radius: 50%; margin-left: auto;}
        .status-dot.ok {background-color: var(--text-good-status);} .status-dot.triggered {background-color: var(--text-warn-status);} .status-dot.offline {background-color: var(--text-secondary);}
        .activity-list { max-height: 200px; overflow-y: auto; padding-right: 8px;}
        .activity-item { padding: 14px 8px; border-bottom: 1px solid rgba(var(--primary-accent-rgb), 0.1); font-size: 14px; color: var(--text-secondary); display: flex; justify-content: space-between; align-items: center;}
        .activity-item:last-child { border-bottom: none;}
        .activity-item .event-icon { margin-right: 10px; color: var(--text-secondary); font-size: 20px;}
        .activity-item .time { font-size: 13px; color: var(--text-secondary);}
        body.dark-mode .activity-item { border-bottom-color: rgba(var(--primary-accent-rgb), 0.2);}


        /* Right Panel */
        .right-panel { grid-area: right-panel; display: flex; flex-direction: column; gap: 18px; padding: 8px; }
        .right-panel .glass-panel { opacity: 0; animation: fadeInElement var(--transition-duration-normal) var(--transition-cubic-elegant) forwards; }
        .right-panel .status-card:nth-child(1) { animation-delay: 0.6s; } .right-panel .status-card:nth-child(2) { animation-delay: 0.65s; } .right-panel .security-card { animation-delay: 0.7s; } .right-panel .isometric-home-container { animation-delay: 0.75s; }
        .status-card { display: flex; align-items: center; gap: 18px; }
        .status-card .material-symbols-outlined { font-size: 26px; color: var(--text-secondary); }
        .status-card:hover .material-symbols-outlined { color: var(--primary-accent); }
        .status-card .info .label { font-size: 13.5px; color: var(--text-secondary); margin-bottom: 5px; }
        .status-card .info .value { font-size: 19px; font-weight: 500; color: var(--text-value); letter-spacing: -0.1px;}
        .status-card.security-card .material-symbols-outlined { font-size: 28px;} /* Color handled by JS */
        .status-card.security-card:hover .material-symbols-outlined { filter: brightness(1.1); } 
        .status-card.security-card .info .label { font-size: 17px; color: var(--text-primary); font-weight: 500;}
        .isometric-home-container { flex-grow: 1; display: flex; align-items: center; justify-content: center; padding: 18px; }
        .isometric-home-container #isometric-house-svg { width: 100%; max-width: 270px; height: auto; filter: drop-shadow(0px 12px 25px rgba(120, 150, 200, 0.22)); transition: transform var(--transition-duration-normal) var(--transition-cubic-elegant), filter var(--transition-duration-normal) var(--transition-cubic-elegant); }
        body.dark-mode .isometric-home-container #isometric-house-svg { filter: drop-shadow(0px 12px 28px rgba(0,0,0, 0.35));}
        .isometric-home-container:hover #isometric-house-svg { transform: scale(1.03) translateY(-3px); }

    </style>
</head>
<body>
    <!-- HTML Structure (Header, Sidebar, Main Content Tabs, Right Panel) -->
    <!-- This will be nearly identical to the previous HTML you provided, -->
    <!-- just ensure IDs for elements controlled by JS are correct. -->
    <!-- Example: radialCurrentTempDisplay, radialSetpointDisplay, etc. for thermostat dial -->
     <div class="dashboard-container">
        <!-- Header (Includes Theme Switcher) -->
        <header class="header">
            <h1>Smart Home</h1>
            <div class="header-actions">
                <button class="icon-button neumorphic-element neumorphic-outset" id="theme-switcher" title="Toggle Theme"><span class="material-symbols-outlined">light_mode</span></button>
                <button class="icon-button neumorphic-element neumorphic-outset" title="Notifications"><span class="material-symbols-outlined">notifications</span></button>
                <div class="user-avatar neumorphic-element neumorphic-outset" title="User Profile"><div class="user-avatar-inner"></div></div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-icon active" data-tab="living-room" title="Living Room"><span class="material-symbols-outlined">home</span></div>
            <div class="sidebar-icon" data-tab="lights-control" title="Lights Control"><span class="material-symbols-outlined">emoji_objects</span></div>
            <div class="sidebar-icon" data-tab="temperature-settings" title="Temperature"><span class="material-symbols-outlined">device_thermostat</span></div>
            <div class="sidebar-icon" data-tab="security-panel" title="Security"><span class="material-symbols-outlined">verified_user</span></div>
        </aside>

        <!-- Main Content Area (Tabs) -->
        <main class="main-content">
            <!-- Living Room Tab -->
            <div id="living-room" class="tab-content active">
                <div class="main-content-card">
                    <h2>Living Room Overview</h2>
                    <div class="room-info-grid">
                        <div class="info-item"><div class="label">Temperature</div><div class="value" id="livingRoomTempDisplay">21.5°C</div></div>
                        <div class="info-item"><div class="label">Humidity</div><div class="value">52%</div></div>
                        <div class="info-item"><div class="label">Air Quality</div><div class="value air-good">Good</div></div>
                    </div>
                    <div class="graph-container"><canvas id="temperatureTrendChart"></canvas></div>
                    <div class="lights-control-section"><span class="label">Main Ambient Lights</span><div class="toggle-switch-outer on" id="mainLivingRoomLightsToggle"><div class="toggle-switch-nub"></div></div></div>
                    <div class="lights-graph-container graph-container"><canvas id="lightsActivityChart"></canvas></div>
                </div>
            </div>

            <!-- Lights Control Tab -->
            <div id="lights-control" class="tab-content">
                <div class="main-content-card">
                    <h2>Lighting Control</h2>
                    <h3>Individual Lights</h3>
                    <div class="lights-tab-grid">
                        <div class="light-control-card neumorphic-element neumorphic-outset"><div class="light-control-card-header"><span class="label">Overhead Lights</span><span class="material-symbols-outlined">ceiling_light</span></div><div class="room-name">Living Room</div><div class="toggle-switch-outer on light-toggle" data-light-id="overhead"><div class="toggle-switch-nub"></div></div></div>
                        <div class="light-control-card neumorphic-element neumorphic-outset"><div class="light-control-card-header"><span class="label">Desk Lamp</span><span class="material-symbols-outlined">table_lamp</span></div><div class="room-name">Office</div><div class="toggle-switch-outer light-toggle" data-light-id="desk"><div class="toggle-switch-nub"></div></div></div>
                        <div class="light-control-card neumorphic-element neumorphic-outset"><div class="light-control-card-header"><span class="label">Kitchen Spots</span><span class="material-symbols-outlined">light_group</span></div><div class="room-name">Kitchen</div><div class="toggle-switch-outer on light-toggle" data-light-id="kitchen"><div class="toggle-switch-nub"></div></div></div>
                        <div class="light-control-card neumorphic-element neumorphic-outset"><div class="light-control-card-header"><span class="label">Bedside Lamp</span><span class="material-symbols-outlined">nightlight</span></div><div class="room-name">Master Bedroom</div><div class="toggle-switch-outer light-toggle" data-light-id="bedside"><div class="toggle-switch-nub"></div></div></div>
                    </div>
                    <h3 style="margin-top:30px;">Lighting Scenes</h3>
                    <div class="scenes-grid">
                        <div class="scene-card neumorphic-element neumorphic-outset active"><span class="material-symbols-outlined">brightness_4</span><span class="label">Evening Relax</span></div>
                        <div class="scene-card neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">movie_filter</span><span class="label">Movie Night</span></div>
                        <div class="scene-card neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">restaurant_menu</span><span class="label">Dinner Ambience</span></div>
                        <div class="scene-card neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">auto_stories</span><span class="label">Reading Nook</span></div>
                    </div>
                </div>
            </div>

            <!-- Temperature Settings Tab -->
            <div id="temperature-settings" class="tab-content">
                <div class="main-content-card">
                     <div class="thermostat-container">
                         <h2>Climate Settings</h2>
                        <div class="radial-thermostat-wrapper">
                            <div class="dial-bg-neumorphic"></div>
                            <svg class="dial-svg-track" viewBox="-10 -10 220 220"> <!-- Adjusted viewBox for stroke -->
                                <path id="thermostatTrackActualPath" d="M 100,10 A 90,90 0 1 1 99.9,10.001" stroke-width="20" /> <!-- Full circle, will be masked by dash -->
                            </svg>
                             <svg class="dial-svg-progress" viewBox="-10 -10 220 220">
                                <path id="thermostatProgressActualPath" d="M 100,10 A 90,90 0 1 1 99.9,10.001" stroke-width="20"/>
                            </svg>
                            <div id="dial-touch-target"></div> <!-- For touch/mouse events -->
                            <div id="dial-drag-handle"><span class="material-symbols-outlined" style="font-size: 18px; color: var(--text-secondary);">drag_handle</span></div>
                            <div class="dial-center-display">
                                <div class="dial-current-temp-value" id="radialCurrentTempDisplay">21.5°</div>
                                <div class="dial-setpoint-label">TARGET: <span id="radialSetpointDisplay">22.0</span>°C</div>
                            </div>
                        </div>

                        <div class="thermostat-controls">
                            <button class="control-button neumorphic-element neumorphic-outset" id="thermostatTempDown" title="Decrease temperature"><span class="material-symbols-outlined">remove_circle_outline</span></button>
                            <button class="control-button neumorphic-element neumorphic-outset" id="thermostatTempUp" title="Increase temperature"><span class="material-symbols-outlined">add_circle_outline</span></button>
                        </div>
                        <div class="thermostat-modes">
                            <button class="mode-button neumorphic-element neumorphic-outset active" data-mode="heat"><span class="material-symbols-outlined">local_fire_department</span>Heat</button>
                            <button class="mode-button neumorphic-element neumorphic-outset" data-mode="cool"><span class="material-symbols-outlined">ac_unit</span>Cool</button>
                            <button class="mode-button neumorphic-element neumorphic-outset" data-mode="fan"><span class="material-symbols-outlined">mode_fan</span>Fan Only</button>
                            <button class="mode-button neumorphic-element neumorphic-outset" data-mode="off"><span class="material-symbols-outlined">power_settings_new</span>Off</button>
                        </div>
                    </div>
                    <h3 style="margin-top: 25px; margin-bottom:15px;">Daily Temperature Log</h3>
                    <div class="graph-container" style="height: 120px;"><canvas id="dailyTempHistoryChart"></canvas></div>
                </div>
            </div>
            
            <!-- Security Panel Tab (structure from previous example, refined here) -->
            <div id="security-panel" class="tab-content">
                 <div class="main-content-card">
                    <h2>Security Hub</h2>
                    <div class="security-status-and-actions">
                        <div class="system-status-prominent">
                            <span class="material-symbols-outlined status-good" id="securityStatusIcon">verified_user</span>
                            <div class="system-status-text-large status-good" id="securityStatusTextLarge">System Disarmed</div>
                            <div class="system-status-subtext" id="securityStatusSubtext">All sensors normal.</div>
                        </div>
                        <div class="security-main-actions">
                            <button class="arm-disarm-button arm neumorphic-element" id="armSystem"><span class="material-symbols-outlined">shield_lock</span>Arm Away</button>
                            <button class="arm-disarm-button disarm neumorphic-element" id="disarmSystem"><span class="material-symbols-outlined">lock_open</span>Disarm System</button>
                        </div>
                    </div>
                    
                    <h3>Sensor Status</h3>
                    <div class="sensor-status-grid">
                        <div class="sensor-item neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">door_front</span><span class="label">Front Door</span> <div class="status-dot ok"></div></div>
                        <div class="sensor-item neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">door_back</span><span class="label">Back Door</span><div class="status-dot ok"></div></div>
                        <div class="sensor-item neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">window</span><span class="label">Living Window</span><div class="status-dot ok"></div></div>
                        <div class="sensor-item neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">sensor_door</span><span class="label">Garage Door</span><div class="status-dot triggered"></div></div>
                        <div class="sensor-item neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">motion_sensor_active</span><span class="label">Hallway Motion</span><div class="status-dot ok"></div></div>
                        <div class="sensor-item neumorphic-element neumorphic-outset"><span class="material-symbols-outlined">camera_indoor</span><span class="label">Indoor Camera</span><div class="status-dot offline"></div></div>
                    </div>

                    <h3>Recent Activity</h3>
                    <div class="activity-list">
                        <div class="activity-item"><span class="material-symbols-outlined event-icon">meeting_room</span><span>Front door opened</span> <span class="time">10:32 AM</span></div>
                        <div class="activity-item"><span class="material-symbols-outlined event-icon">lock_open</span><span>System disarmed by Main User</span> <span class="time">10:31 AM</span></div>
                        <div class="activity-item"><span class="material-symbols-outlined event-icon">shield_lock</span><span>System armed (Away mode)</span> <span class="time">08:15 AM</span></div>
                        <div class="activity-item"><span class="material-symbols-outlined event-icon">sensors</span><span>Back window sensor closed</span> <span class="time">Yesterday 09:00 PM</span></div>
                        <div class="activity-item"><span class="material-symbols-outlined event-icon">directions_run</span><span>Motion detected in hallway</span> <span class="time">Yesterday 08:55 PM</span></div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Right Panel -->
        <aside class="right-panel">
            <div class="status-card glass-panel"><span class="material-symbols-outlined">emoji_objects</span><div class="info"><div class="label">Active Lights</div><div class="value" id="activeLightsCountDisplay">3</div></div></div>
            <div class="status-card glass-panel"><span class="material-symbols-outlined">device_thermostat</span><div class="info"><div class="label">Indoor Temp.</div><div class="value" id="indoorTempRightPanelDisplay">21.5°C</div></div></div>
            <div class="status-card security-card glass-panel"><span class="material-symbols-outlined" id="securityIconRightPanel">verified_user</span><div class="info"><div class="label" id="securityStatusRightPanelDisplay">Security: Disarmed</div></div></div>
            <div class="isometric-home-container glass-panel"><svg id="isometric-house-svg" viewBox="0 0 200 170" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="roofGradientMain" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" class="roof-grad-stop1"/><stop offset="100%" class="roof-grad-stop2"/></linearGradient><linearGradient id="wallGradientMain" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" class="wall-grad-stop1"/><stop offset="100%" class="wall-grad-stop2"/></linearGradient><style>.win-glass-fill{fill:var(--primary-accent-win-glass);opacity:0.75;}.win-frame-stroke{stroke:var(--primary-accent-win-frame-stroke);stroke-width:0.4;fill:var(--primary-accent-win-frame-fill);}.door-main-fill{fill:var(--primary-accent-door-fill);}.roof-stroke-main{stroke:var(--primary-accent-roof-stroke);stroke-width:0.5;}.wall-stroke-main{stroke:var(--primary-accent-wall-stroke);stroke-width:0.4;}</style></defs><polygon id="house-base" points="10,138 100,168 190,138 100,108" fill="var(--house-base-fill)" stroke="var(--house-base-stroke)" stroke-width="1"/><polygon points="100,10 0,60 100,110 200,60" fill="url(#roofGradientMain)" class="roof-stroke-main"/><polygon points="0,60 0,135 100,165 100,110" fill="url(#wallGradientMain)" class="wall-stroke-main"/><polygon id="right-wall" points="100,110 100,165 200,135 200,60" fill="var(--house-right-wall-fill)" class="wall-stroke-main"/><polygon points="160,103 160,132 178,122 178,93" class="door-main-fill"/><rect x="163" y="112" width="4" height="7" fill="var(--house-handle-fill)"/><rect x="118" y="78" width="28" height="18" class="win-frame-stroke"/><rect x="120" y="80" width="10" height="14" class="win-glass-fill"/><rect x="134" y="80" width="10" height="14" class="win-glass-fill"/><line x1="120" y1="87" x2="144" y2="87" stroke="var(--house-line-stroke)" stroke-width="0.3"/><polygon points="20,78 48,92 48,110 20,96" class="win-frame-stroke"/><polygon points="23,80.5 44,92.5 44,107 23,95" class="win-glass-fill"/><polygon points="20,103 48,117 48,132 20,118" class="win-frame-stroke"/><polygon points="23,105.5 44,117.5 44,129 23,117" class="win-glass-fill"/><polygon points="90,48 78,58 90,68 102,58" fill="url(#roofGradientMain)" class="roof-stroke-main"/><polygon id="dormer-left" points="78,58 78,69 90,76 90,68" fill="var(--house-dormer-left-fill)" class="wall-stroke-main"/><polygon id="dormer-right" points="90,68 90,76 102,69 102,58" fill="var(--house-dormer-right-fill)" class="wall-stroke-main"/><rect x="82" y="60.5" width="16" height="9" class="win-frame-stroke"/><rect x="83.5" y="62" width="13" height="6" class="win-glass-fill"/></svg></div>
        </aside>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const themeSwitcher = document.getElementById('theme-switcher');
        const themeSwitcherIcon = themeSwitcher.querySelector('.material-symbols-outlined');
        const body = document.body;

        function applyTheme(theme, isInitial = false) {
            body.classList.toggle('dark-mode', theme === 'dark');
            themeSwitcherIcon.textContent = theme === 'dark' ? 'dark_mode' : 'light_mode';
            if (!isInitial) {
                Object.values(window.charts || {}).forEach(chart => chart?.destroy());
                initializeCharts();
            }
        }
        let savedTheme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        applyTheme(savedTheme, true);

        themeSwitcher.addEventListener('click', () => {
            const newTheme = body.classList.contains('dark-mode') ? 'light' : 'dark';
            applyTheme(newTheme);
            localStorage.setItem('theme', newTheme);
        });

        // Tabs, Toggles, Scenes
        const sidebarIcons = document.querySelectorAll('.sidebar .sidebar-icon');
        const tabContents = document.querySelectorAll('.main-content .tab-content');
        const allToggleSwitches = document.querySelectorAll('.toggle-switch-outer');
        const sceneCards = document.querySelectorAll('.scenes-grid .scene-card');
        const activeLightsCountDisplay = document.getElementById('activeLightsCountDisplay');

        function updateActiveLightsCount() {
            const activeLights = document.querySelectorAll('#lights-control .light-toggle.on').length + 
                                 (document.getElementById('mainLivingRoomLightsToggle')?.classList.contains('on') ? 1 : 0);
            if(activeLightsCountDisplay) activeLightsCountDisplay.textContent = activeLights;
        }

        sidebarIcons.forEach(icon => {
            icon.addEventListener('click', () => {
                sidebarIcons.forEach(i => i.classList.remove('active'));
                icon.classList.add('active');
                const targetTabId = icon.dataset.tab;
                tabContents.forEach(tab => tab.classList.toggle('active', tab.id === targetTabId));
            });
        });
        allToggleSwitches.forEach(toggle => {
            toggle.addEventListener('click', () => {
                 toggle.classList.toggle('on');
                 if (toggle.classList.contains('light-toggle') || toggle.id === 'mainLivingRoomLightsToggle') {
                    updateActiveLightsCount();
                }
            });
        });
        sceneCards.forEach(card => card.addEventListener('click', () => { sceneCards.forEach(c => c.classList.remove('active')); card.classList.add('active');}));
        updateActiveLightsCount(); // Initial count

        // Radial Thermostat
        const radialThermostatWrapper = document.querySelector('.radial-thermostat-wrapper');
        const dialDragHandle = document.getElementById('dial-drag-handle');
        const dialTouchTarget = document.getElementById('dial-touch-target');
        const progressPathEl = document.getElementById('thermostatProgressActualPath');
        const radialSetpointDisplay = document.getElementById('radialSetpointDisplay');
        const radialCurrentTempDisplay = document.getElementById('radialCurrentTempDisplay'); // Assuming this could be dynamic
        const indoorTempRightPanelDisplay = document.getElementById('indoorTempRightPanelDisplay');
        const livingRoomTempDisplay = document.getElementById('livingRoomTempDisplay'); // Display in Living Room tab

        const TEMP_MIN = 15, TEMP_MAX = 30, DIAL_ANGLE_OFFSET = -135, DIAL_ANGLE_RANGE = 270;
        let currentRadialSetpoint = 22.0;
        const pathTotalLength = progressPathEl ? progressPathEl.getTotalLength() : 0;
         if (progressPathEl) progressPathEl.style.strokeDasharray = pathTotalLength;


        function updateRadialDialVisuals(value) {
            if (!radialThermostatWrapper || !progressPathEl || !dialDragHandle) return;
            
            const percentage = (value - TEMP_MIN) / (TEMP_MAX - TEMP_MIN);
            const safePercentage = Math.max(0, Math.min(1, percentage)); // Clamp between 0 and 1
            const angleDeg = DIAL_ANGLE_OFFSET + safePercentage * DIAL_ANGLE_RANGE;
            const angleRad = angleDeg * (Math.PI / 180);

            // For stroke-dashoffset, 0 means full. So pathLength * (1 - percentage for THIS ARC)
            // The visual arc is 270 degrees. So max visible portion is (270/360) * pathTotalLength
            progressPathEl.style.strokeDashoffset = pathTotalLength * (1 - safePercentage * (DIAL_ANGLE_RANGE / 360));


            const dialRadius = radialThermostatWrapper.offsetWidth / 2;
            const handleRadius = dialDragHandle.offsetWidth / 2;
            // Position handle on the center of the track path
            const trackMidRadius = dialRadius - (parseFloat(getComputedStyle(progressPathEl).strokeWidth) / 2 || 11); // 11 is half of 22 (stroke-width)
            
            const x = dialRadius + trackMidRadius * Math.cos(angleRad) - handleRadius;
            const y = dialRadius + trackMidRadius * Math.sin(angleRad) - handleRadius;
            dialDragHandle.style.transform = `translate(${x}px, ${y}px)`;

            if (radialSetpointDisplay) radialSetpointDisplay.textContent = value.toFixed(1);
            // Update other displays
            if (indoorTempRightPanelDisplay && radialCurrentTempDisplay?.textContent === currentRadialSetpoint.toFixed(1) + "°") { // only if matches to simulate it
                indoorTempRightPanelDisplay.textContent = value.toFixed(1) + "°C";
            }
            if(livingRoomTempDisplay && radialCurrentTempDisplay?.textContent === currentRadialSetpoint.toFixed(1) + "°"){
                 livingRoomTempDisplay.textContent = value.toFixed(1) + "°C";
            }
        }


        let isDialDragging = false;
        function handleDialInteraction(clientX, clientY) {
            if (!isDialDragging || !radialThermostatWrapper) return;
            const rect = radialThermostatWrapper.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            let angleRad = Math.atan2(clientY - centerY, clientX - centerX);
            let angleDegInteraction = angleRad * (180 / Math.PI); // This is standard Math angle (0 right, positive CCW)
            
            // Convert this to our SVG/Dial coordinate system (0 is top, positive CW, starting at -135 offset)
            let dialAngle = angleDegInteraction - 90; // Shift so 0 is top
            if (dialAngle < 0) dialAngle += 360; // Ensure positive

            // Now map this to our -135 to +135 range (which is what angleOffset and angleRange mean visually)
            let mappedAngle = dialAngle;
            if (dialAngle > 180 && dialAngle < (DIAL_ANGLE_OFFSET + 360 + DIAL_ANGLE_RANGE)%360) { // Dead zone bottom
                mappedAngle = (clientX > centerX) ? (DIAL_ANGLE_OFFSET + DIAL_ANGLE_RANGE) % 360 : (DIAL_ANGLE_OFFSET + 360) % 360;
            }
             mappedAngle = Math.max(0, Math.min(mappedAngle, DIAL_ANGLE_RANGE)); // Clamp to the 0-270 visual range
            
            let percentage = mappedAngle / DIAL_ANGLE_RANGE;


            currentRadialSetpoint = TEMP_MIN + percentage * (TEMP_MAX - TEMP_MIN);
            currentRadialSetpoint = Math.round(currentRadialSetpoint * 2) / 2; // Snap to 0.5
            currentRadialSetpoint = Math.max(TEMP_MIN, Math.min(currentRadialSetpoint, TEMP_MAX));
            updateRadialDialVisuals(currentRadialSetpoint);
        }
        
        // Recalculate effective track for dragging relative to SVG's path drawing
        const startAngleSVG = -45; // Corresponds to -135 visual
        const endAngleSVG = 225;   // Corresponds to +135 visual

        function handleDialInteractionNew(clientX, clientY) {
            if (!isDialDragging || !radialThermostatWrapper) return;
            const rect = radialThermostatWrapper.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            let angleRad = Math.atan2(clientY - centerY, clientX - centerX); // Angle from center, 0 to the right
            let angleDeg = (angleRad * 180 / Math.PI + 360) % 360; // Normalize to 0-360, 0 right

            // Map this angle to our visual arc range (-135 to 135, where -135 is SVG start of visual arc)
            // Our SVG path effectively starts visually at -135 degrees (bottom-left-ish)
            // and ends at 135 degrees (bottom-right-ish).
            // The SVG coordinates make path 'd' attribute easier if start at SVG 0 deg (top) and rotate the whole SVG group.
            // Here, using path `d` directly, a standard math angle (0=right)
            // needs mapping to percentage of our _visual_ arc.

            // Target angles relative to standard horizontal x-axis, positive CCW
            const visualArcStartDeg = 135; // bottom left from dial pov, Math angle
            const visualArcEndDeg = 45;   // bottom right, math angle for end, crossing 0/360
                                         // The arc visually sweeps from bottom-left, up, to bottom-right
                                         // This is hard to map with simple atan2.

            // Simpler: if using the rotated SVG system:
            let angleFromTopDeg = (angleDeg - 90 + 360) % 360; // Angle from top, clockwise
            let percentage = 0;
            if (angleFromTopDeg >= 0 && angleFromTopDeg <= DIAL_ANGLE_RANGE) {
                percentage = angleFromTopDeg / DIAL_ANGLE_RANGE;
            } else if (angleFromTopDeg > DIAL_ANGLE_RANGE && angleFromTopDeg < (DIAL_ANGLE_RANGE + (360-DIAL_ANGLE_RANGE)/2) ) { // closer to end
                 percentage = 1;
            } else { // closer to start
                 percentage = 0;
            }


            currentRadialSetpoint = TEMP_MIN + percentage * (TEMP_MAX - TEMP_MIN);
            currentRadialSetpoint = Math.round(currentRadialSetpoint * 2) / 2; // Snap
            currentRadialSetpoint = Math.max(TEMP_MIN, Math.min(currentRadialSetpoint, TEMP_MAX));
            updateRadialDialVisuals(currentRadialSetpoint);
        }


        dialTouchTarget?.addEventListener('mousedown', (e) => { isDialDragging = true; dialDragHandle.style.cursor = 'grabbing'; handleDialInteractionNew(e.clientX, e.clientY); });
        dialTouchTarget?.addEventListener('touchstart', (e) => { isDialDragging = true; handleDialInteractionNew(e.touches[0].clientX, e.touches[0].clientY); e.preventDefault(); }, { passive: false });
        document.addEventListener('mousemove', (e) => handleDialInteractionNew(e.clientX, e.clientY));
        document.addEventListener('touchmove', (e) => { if (isDialDragging) {handleDialInteractionNew(e.touches[0].clientX, e.touches[0].clientY); e.preventDefault();}}, { passive: false });
        document.addEventListener('mouseup', () => { if(isDialDragging) {isDialDragging = false; if(dialDragHandle) dialDragHandle.style.cursor = 'grab';} });
        document.addEventListener('touchend', () => { if(isDialDragging) {isDialDragging = false;} });
        
        updateRadialDialVisuals(currentRadialSetpoint); // Initial position

        // +/- buttons for thermostat
        const tempUpButtonTherm = document.getElementById('thermostatTempUp');
        const tempDownButtonTherm = document.getElementById('thermostatTempDown');
        
        tempUpButtonTherm?.addEventListener('click', () => { currentRadialSetpoint = Math.min(TEMP_MAX, currentRadialSetpoint + 0.5); updateRadialDialVisuals(currentRadialSetpoint); });
        tempDownButtonTherm?.addEventListener('click', () => { currentRadialSetpoint = Math.max(TEMP_MIN, currentRadialSetpoint - 0.5); updateRadialDialVisuals(currentRadialSetpoint); });

        const thermostatModeButtons = document.querySelectorAll('#temperature-settings .mode-button');
        thermostatModeButtons.forEach(button => button.addEventListener('click', () => { thermostatModeButtons.forEach(btn => btn.classList.remove('active')); button.classList.add('active'); }));


        // Security Tab
        const armButton = document.getElementById('armSystem');
        const disarmButton = document.getElementById('disarmSystem');
        const securityStatusIcon = document.getElementById('securityStatusIcon');
        const securityStatusTextLarge = document.getElementById('securityStatusTextLarge');
        const securityStatusSubtext = document.getElementById('securityStatusSubtext');
        const securityInfoRightPanelDisplay = document.getElementById('securityStatusRightPanelDisplay');
        const securityIconRightPanelDisplay = document.getElementById('securityIconRightPanel');


        function setSecurityStatus(isArmed) {
            if (isArmed) {
                if (securityStatusIcon) { securityStatusIcon.classList.remove('status-good'); securityStatusIcon.classList.add('status-alert'); securityStatusIcon.textContent = 'shield_lock'; }
                if (securityStatusTextLarge) {securityStatusTextLarge.textContent = 'System Armed'; securityStatusTextLarge.className = 'system-status-text-large status-alert';}
                if (securityStatusSubtext) securityStatusSubtext.textContent = 'Mode: Away. Monitoring active.';
                if (securityInfoRightPanelDisplay) securityInfoRightPanelDisplay.textContent = 'Security: Armed';
                if (securityIconRightPanelDisplay) { securityIconRightPanelDisplay.textContent = 'shield_lock'; securityIconRightPanelDisplay.style.color = 'var(--text-alert-status)';}
            } else {
                if (securityStatusIcon) { securityStatusIcon.classList.remove('status-alert'); securityStatusIcon.classList.add('status-good'); securityStatusIcon.textContent = 'verified_user'; }
                if (securityStatusTextLarge) {securityStatusTextLarge.textContent = 'System Disarmed'; securityStatusTextLarge.className = 'system-status-text-large status-good';}
                if (securityStatusSubtext) securityStatusSubtext.textContent = 'All sensors normal.';
                if (securityInfoRightPanelDisplay) securityInfoRightPanelDisplay.textContent = 'Security: Disarmed';
                if (securityIconRightPanelDisplay) { securityIconRightPanelDisplay.textContent = 'verified_user'; securityIconRightPanelDisplay.style.color = 'var(--text-good-status)';}
            }
        }
        armButton?.addEventListener('click', () => setSecurityStatus(true));
        disarmButton?.addEventListener('click', () => setSecurityStatus(false));
        setSecurityStatus(false); // Initial

        // Chart Initialization
        window.charts = {}; // Store chart instances

        function initializeCharts() {
            const rootStyles = getComputedStyle(document.documentElement);
            const currentGraphLineColor = rootStyles.getPropertyValue('--graph-line-color').trim();
            const currentGraphFillStart = rootStyles.getPropertyValue('--graph-fill-start-rgba').trim();
            const currentGraphFillEnd = rootStyles.getPropertyValue('--graph-fill-end-rgba').trim();
            const currentGraphTickColor = rootStyles.getPropertyValue('--graph-tick-color').trim();

            const imageAccurateChartOptions = (isFilled = false, dataMin, dataMax, hasXTicks = true) => ({
                responsive: true, maintainAspectRatio: false,
                plugins: { legend: { display: false }, tooltip: { enabled: false } },
                scales: { x: { display: true, grid: { display: false, drawBorder: false, drawTicks: hasXTicks, tickMarkLength: 8, color: 'transparent' }, ticks: { display: true, callback: () => '', color: currentGraphTickColor, padding: 4, major: { enabled: false } } }, y: { display: false, min: dataMin, max: dataMax } },
                elements: { line: { tension: 0.45, borderColor: currentGraphLineColor, borderWidth: 2.2, fill: isFilled ? {target: 'origin', above: context => { const chart = context.chart; const {ctx, chartArea} = chart; if (!chartArea) return null; const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top); gradient.addColorStop(0, currentGraphFillEnd); gradient.addColorStop(0.8, currentGraphFillStart); return gradient;}} : false }, point: { radius: 0 } },
                animation: { duration: 900, easing: 'cubicBezier(0.4, 0, 0.2, 1)' }
            });
            
            const chartDataSets = {
                livingRoomTemp: [20.5, 21.8, 20.8, 22.2, 21.2, 22.8, 21.7, 23.0, 22.0, 21.2, 22.5, 21.5],
                livingRoomLights: [8, 11, 9.5, 13, 10, 14, 11.5, 15, 12.5, 13.5, 16, 14],
                dailyTemp: [18, 18.5, 19, 20, 21, 22, 22.5, 22, 21, 19.5, 19, 18.5, 18, 18.2, 18.8, 19.5, 20.5, 21.5, 22, 21.5, 20.8, 20, 19.2, 18.5]
            };

            const tempTrendCtx = document.getElementById('temperatureTrendChart')?.getContext('2d');
            if(tempTrendCtx){ window.charts.temperatureChartInstance = new Chart(tempTrendCtx, { type: 'line', data: { labels: chartDataSets.livingRoomTemp.map((_,i) => i), datasets: [{ data: chartDataSets.livingRoomTemp }] }, options: imageAccurateChartOptions(false, Math.min(...chartDataSets.livingRoomTemp) -1, Math.max(...chartDataSets.livingRoomTemp) +1) }); }

            const lightsActivityCtx = document.getElementById('lightsActivityChart')?.getContext('2d');
            if(lightsActivityCtx){ window.charts.lightsChartInstance = new Chart(lightsActivityCtx, { type: 'line', data: { labels: chartDataSets.livingRoomLights.map((_,i) => i), datasets: [{ data: chartDataSets.livingRoomLights }] }, options: imageAccurateChartOptions(true, 0, Math.max(...chartDataSets.livingRoomLights) + 5) }); }
            
            const dailyTempCtx = document.getElementById('dailyTempHistoryChart')?.getContext('2d');
            if(dailyTempCtx){ window.charts.dailyTempChartInstance = new Chart(dailyTempCtx, { type: 'line', data: { labels: chartDataSets.dailyTemp.map((_,i) => i), datasets: [{ data: chartDataSets.dailyTemp }] }, options: imageAccurateChartOptions(true, Math.min(...chartDataSets.dailyTemp) -2, Math.max(...chartDataSets.dailyTemp) +2, true) }); }
        }
        initializeCharts(); 
    });
    </script>
</body>
</html>