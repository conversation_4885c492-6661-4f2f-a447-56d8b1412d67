from PySide6 import QtWidgets, QtCore, QtGui
import sys
import os
import json
from power_monitor_service import PowerMonitorService


class PowerNotificationWidget(QtWidgets.QWidget):
    """A sleek, physics-based notification widget for power connection status."""
    
    def __init__(self):
        super().__init__()
        
        # Load configuration
        self.config = self.load_config()
        
        # Initialize power monitor
        self.power_monitor = PowerMonitorService(self.config.get('polling_interval', 1000))
        self.power_monitor.power_connected.connect(self.show_notification)
        
        # Animation objects
        self.slide_animation = None
        self.fade_animation = None
        self.animation_group = None
        
        # Auto-dismiss timer
        self.dismiss_timer = QtCore.QTimer(self)
        self.dismiss_timer.setSingleShot(True)
        self.dismiss_timer.timeout.connect(self.hide_notification)
        
        # State tracking
        self.is_visible = False
        self.is_animating = False
        
        self.init_ui()
        
    def load_config(self):
        """Load configuration from JSON file."""
        config_path = os.path.join(os.path.dirname(__file__), "power_notification_config.json")
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()
            
    def get_default_config(self):
        """Return default configuration."""
        return {
            "animation_duration": 1000,
            "display_duration": 4000,
            "notification_position": {"x": "center", "y": 20},
            "colors": {
                "background": "rgba(0, 0, 0, 0.9)",
                "text": "#FFFFFF",
                "accent": "#10B981",
                "border": "rgba(255, 255, 255, 0.2)"
            },
            "pill_dimensions": {"width": 300, "height": 50},
            "font_size": 14,
            "icon_size": 20,
            "border_radius": 25,
            "polling_interval": 1000,
            "fade_duration": 500
        }
        
    def init_ui(self):
        """Initialize the user interface."""
        # Window setup
        self.setWindowFlags(
            QtCore.Qt.FramelessWindowHint |
            QtCore.Qt.Tool |
            QtCore.Qt.WindowStaysOnTopHint
        )
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        
        # Set dimensions
        width = self.config['pill_dimensions']['width']
        height = self.config['pill_dimensions']['height']
        self.setFixedSize(width, height)
        
        # Create layout
        self.main_layout = QtWidgets.QHBoxLayout(self)
        padding = self.config.get('padding', {'horizontal': 20, 'vertical': 12})
        spacing = self.config.get('spacing', 12)
        self.main_layout.setContentsMargins(
            padding['horizontal'], padding['vertical'],
            padding['horizontal'], padding['vertical']
        )
        self.main_layout.setSpacing(spacing)
        
        # Icon label
        self.icon_label = QtWidgets.QLabel()
        self.icon_label.setObjectName("icon_label")
        self.icon_label.setAlignment(QtCore.Qt.AlignCenter)
        self.load_power_icon()
        self.main_layout.addWidget(self.icon_label)

        # Text label
        self.text_label = QtWidgets.QLabel("Power Connected")
        self.text_label.setObjectName("text_label")
        self.text_label.setAlignment(QtCore.Qt.AlignCenter)
        self.main_layout.addWidget(self.text_label)
        
        # Apply styling
        self.apply_styles()
        
        # Position widget off-screen initially
        self.position_widget(hidden=True)
        
        # Hide initially
        self.hide()
        
    def load_power_icon(self):
        """Load the power/charging icon with enhanced styling."""
        icon_path = os.path.join(os.path.dirname(__file__), "images", "battery-bolt.svg")
        icon_size = self.config.get('icon_size', 24)

        if os.path.exists(icon_path):
            # Load SVG icon
            pixmap = QtGui.QPixmap(icon_path)
            if not pixmap.isNull():
                # Create a colored version of the icon
                colored_pixmap = self.create_colored_icon(pixmap, self.config['colors']['accent'])
                scaled_pixmap = colored_pixmap.scaled(
                    icon_size, icon_size,
                    QtCore.Qt.KeepAspectRatio,
                    QtCore.Qt.SmoothTransformation
                )
                self.icon_label.setPixmap(scaled_pixmap)
            else:
                self.set_fallback_icon()
        else:
            # Fallback to styled emoji if icon not found
            self.set_fallback_icon()

    def create_colored_icon(self, pixmap, color):
        """Create a colored version of the icon."""
        colored_pixmap = QtGui.QPixmap(pixmap.size())
        colored_pixmap.fill(QtCore.Qt.transparent)

        painter = QtGui.QPainter(colored_pixmap)
        painter.setCompositionMode(QtGui.QPainter.CompositionMode_SourceOver)
        painter.drawPixmap(0, 0, pixmap)
        painter.setCompositionMode(QtGui.QPainter.CompositionMode_SourceIn)
        painter.fillRect(colored_pixmap.rect(), QtGui.QColor(color))
        painter.end()

        return colored_pixmap

    def set_fallback_icon(self):
        """Set fallback icon with styling."""
        self.icon_label.setText("⚡")
        self.icon_label.setStyleSheet(f"""
            QLabel {{
                color: {self.config['colors']['accent']};
                font-size: {self.config.get('icon_size', 24)}px;
                font-weight: bold;
            }}
        """)
            
    def apply_styles(self):
        """Apply premium glass morphism styling to the widget."""
        colors = self.config['colors']
        border_radius = self.config['border_radius']
        font_size = self.config['font_size']
        font_weight = self.config.get('font_weight', '600')
        border_width = self.config.get('border_width', 1)

        # Enhanced glass morphism style with gradient and shadow effects
        style = f"""
        PowerNotificationWidget {{
            background: {colors.get('background_gradient', colors['background'])};
            border-radius: {border_radius}px;
            border: {border_width}px solid {colors['border']};
        }}
        QLabel {{
            color: {colors['text']};
            font-size: {font_size}px;
            font-weight: {font_weight};
            background: transparent;
            border: none;
            font-family: 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
        }}
        QLabel#text_label {{
            font-weight: {font_weight};
            letter-spacing: 0.5px;
        }}
        QLabel#icon_label {{
            padding: 2px;
        }}
        """
        self.setStyleSheet(style)

        # Apply drop shadow effect
        self.apply_shadow_effect()

    def apply_shadow_effect(self):
        """Apply drop shadow effect to the widget."""
        shadow = QtWidgets.QGraphicsDropShadowEffect()
        shadow.setBlurRadius(self.config.get('blur_radius', 10))
        shadow.setColor(QtGui.QColor(0, 0, 0, 80))  # Semi-transparent black
        shadow.setOffset(0, 4)  # Slight downward offset
        self.setGraphicsEffect(shadow)
        
    def position_widget(self, hidden=False):
        """Position the widget on screen."""
        # Get screen geometry
        screen = QtWidgets.QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        
        # Calculate horizontal center
        x = (screen_geometry.width() - self.width()) // 2
        
        # Calculate vertical position
        if hidden:
            y = -self.height()  # Above screen
        else:
            y = self.config['notification_position']['y']
            
        self.move(x, y)

    def show_notification(self):
        """Show the notification with spring animation."""
        if self.is_visible or self.is_animating:
            return

        self.is_animating = True

        # Position widget above screen
        self.position_widget(hidden=True)
        self.show()
        self.setWindowOpacity(0.0)

        # Create animation group
        self.animation_group = QtCore.QParallelAnimationGroup()

        # Slide animation (geometry)
        self.slide_animation = QtCore.QPropertyAnimation(self, b"geometry")
        self.slide_animation.setDuration(self.config['animation_duration'])
        self.slide_animation.setEasingCurve(QtCore.QEasingCurve.OutBounce)

        # Start and end positions
        start_rect = self.geometry()
        screen = QtWidgets.QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = self.config['notification_position']['y']
        end_rect = QtCore.QRect(x, y, self.width(), self.height())

        self.slide_animation.setStartValue(start_rect)
        self.slide_animation.setEndValue(end_rect)

        # Fade animation (opacity)
        self.fade_animation = QtCore.QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(self.config['animation_duration'])
        self.fade_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(0.95)

        # Add animations to group
        self.animation_group.addAnimation(self.slide_animation)
        self.animation_group.addAnimation(self.fade_animation)

        # Connect animation finished signal
        self.animation_group.finished.connect(self.on_show_animation_finished)

        # Start animation
        self.animation_group.start()

    def on_show_animation_finished(self):
        """Called when show animation completes."""
        self.is_animating = False
        self.is_visible = True

        # Start auto-dismiss timer
        self.dismiss_timer.start(self.config['display_duration'])

    def hide_notification(self):
        """Hide the notification with fade animation."""
        if not self.is_visible or self.is_animating:
            return

        self.is_animating = True
        self.dismiss_timer.stop()

        # Create fade out animation
        self.fade_animation = QtCore.QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(self.config['fade_duration'])
        self.fade_animation.setEasingCurve(QtCore.QEasingCurve.OutCubic)
        self.fade_animation.setStartValue(self.windowOpacity())
        self.fade_animation.setEndValue(0.0)

        # Connect animation finished signal
        self.fade_animation.finished.connect(self.on_hide_animation_finished)

        # Start animation
        self.fade_animation.start()

    def on_hide_animation_finished(self):
        """Called when hide animation completes."""
        self.hide()
        self.is_animating = False
        self.is_visible = False

    def mousePressEvent(self, event):
        """Handle mouse press for click-to-dismiss."""
        if event.button() == QtCore.Qt.LeftButton:
            self.hide_notification()
            event.accept()

    def enterEvent(self, event):
        """Handle mouse enter for hover effect."""
        if self.is_visible and not self.is_animating:
            # Subtle scale up effect
            self.setProperty("hovered", True)
            self.style().polish(self)
        event.accept()

    def leaveEvent(self, event):
        """Handle mouse leave for hover effect."""
        if self.is_visible and not self.is_animating:
            # Return to normal scale
            self.setProperty("hovered", False)
            self.style().polish(self)
        event.accept()

    def start_monitoring(self):
        """Start power monitoring."""
        self.power_monitor.start_monitoring()

    def stop_monitoring(self):
        """Stop power monitoring."""
        self.power_monitor.stop_monitoring()

    def trigger_test_notification(self):
        """Trigger a test notification (for testing purposes)."""
        self.show_notification()

    def closeEvent(self, event):
        """Handle widget close event."""
        self.stop_monitoring()
        if self.animation_group:
            self.animation_group.stop()
        if self.fade_animation:
            self.fade_animation.stop()
        self.dismiss_timer.stop()
        event.accept()
