from PySide6 import QtWidgets, Qt<PERSON>ore, QtGui
import sys
import os
import json
import requests

class WeatherSettingsDialog(QtWidgets.QDialog):
    def __init__(self, parent):
        super().__init__(parent)
        self.parent_widget = parent
        self.setWindowTitle("Weather Widget Settings")
        self.setWindowFlags(self.windowFlags() & ~QtCore.Qt.WindowContextHelpButtonHint)
        self.init_ui()

    def init_ui(self):
        layout = QtWidgets.QFormLayout(self)

        # Color picker button
        self.color_button = QtWidgets.QPushButton("Choose Text Color")
        self.color_button.clicked.connect(self.choose_color)
        layout.addRow("Text Color:", self.color_button)

        # Spacing controls (example, will need adjustment for weather labels)
        self.temp_spacing = QtWidgets.QSpinBox()
        self.temp_spacing.setRange(0, 100)
        self.temp_spacing.setValue(self.parent_widget.temp_spacing)
        layout.addRow("Temperature Letter Spacing:", self.temp_spacing)

        self.cond_spacing = QtWidgets.QSpinBox()
        self.cond_spacing.setRange(0, 50)
        self.cond_spacing.setValue(self.parent_widget.cond_spacing)
        layout.addRow("Condition Letter Spacing:", self.cond_spacing)

        self.loc_spacing = QtWidgets.QSpinBox()
        self.loc_spacing.setRange(0, 50)
        self.loc_spacing.setValue(self.parent_widget.loc_spacing)
        layout.addRow("Location Letter Spacing:", self.loc_spacing)

        # Widget size controls
        size_layout = QtWidgets.QHBoxLayout()
        self.width_spin = QtWidgets.QSpinBox()
        self.width_spin.setRange(100, 1000)
        self.width_spin.setValue(self.parent_widget.widget_width)
        self.height_spin = QtWidgets.QSpinBox()
        self.height_spin.setRange(50, 800)
        self.height_spin.setValue(self.parent_widget.widget_height)
        size_layout.addWidget(QtWidgets.QLabel("W:"))
        size_layout.addWidget(self.width_spin)
        size_layout.addSpacing(20)
        size_layout.addWidget(QtWidgets.QLabel("H:"))
        size_layout.addWidget(self.height_spin)
        layout.addRow("Widget Size:", size_layout)

        # New: Horizontal Layout Toggle
        self.horizontal_layout_checkbox = QtWidgets.QCheckBox("Enable Horizontal Layout")
        self.horizontal_layout_checkbox.setChecked(self.parent_widget.horizontal_layout_enabled)
        layout.addRow("Layout Style:", self.horizontal_layout_checkbox)

        # Buttons
        buttons = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Save | QtWidgets.QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.apply_settings)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)

    def choose_color(self):
        r, g, b, a = self.parent_widget.text_color
        initial = QtGui.QColor(r, g, b, a)
        color = QtWidgets.QColorDialog.getColor(initial, self, "Select Text Color")
        if color.isValid():
            self.selected_color = color
            pix = QtGui.QPixmap(16, 16)
            pix.fill(color)
            self.color_button.setIcon(QtGui.QIcon(pix))
        else:
            self.selected_color = None

    def apply_settings(self):
        if hasattr(self, "selected_color") and self.selected_color:
            r, g, b, a = self.selected_color.getRgb()
            self.parent_widget.text_color = (r, g, b, a)
        self.parent_widget.temp_spacing = self.temp_spacing.value()
        self.parent_widget.cond_spacing = self.cond_spacing.value()
        self.parent_widget.loc_spacing = self.loc_spacing.value()
        self.parent_widget.widget_width = self.width_spin.value()
        self.parent_widget.widget_height = self.height_spin.value()
        # Update the new setting
        self.parent_widget.horizontal_layout_enabled = self.horizontal_layout_checkbox.isChecked()

        self.parent_widget.resize(
            self.parent_widget.widget_width,
            self.parent_widget.widget_height
        )

        # Re-initialize UI and update styles based on new layout setting
        self.parent_widget.init_ui()
        self.parent_widget.update_styles()

        self.parent_widget.save_settings()
        self.accept()


class WeatherWidget(QtWidgets.QWidget):
    API_KEY = "a9f40eb6f56352f238679c2a66b04951"
    DEFAULT_CITY = "Singapore"
    WEATHER_API_URL = "http://api.openweathermap.org/data/2.5/weather?q={city}&appid={api_key}&units=metric"

    def __init__(self):
        super().__init__()
        # Default settings (example values)
        self.text_color = (255, 255, 255, 230)  # White with some transparency
        self.temp_spacing = 20
        self.cond_spacing = 4
        self.loc_spacing = 6
        self.widget_width = 400
        self.widget_height = 150
        self.horizontal_layout_enabled = False # New attribute for the layout

        self.load_settings()
        self.init_ui()

    def load_settings(self):
        CONFIG_PATH = os.path.join(os.path.dirname(__file__), "weather_widget_config.json")
        if os.path.exists(CONFIG_PATH):
            try:
                with open(CONFIG_PATH, "r") as f:
                    data = json.load(f)
                    self.text_color = tuple(data.get("text_color", self.text_color))
                    self.temp_spacing = data.get("temp_spacing", self.temp_spacing)
                    self.cond_spacing = data.get("cond_spacing", self.cond_spacing)
                    self.loc_spacing = data.get("loc_spacing", self.loc_spacing)
                    self.widget_width = data.get("widget_width", self.widget_width)
                    self.widget_height = data.get("widget_height", self.widget_height)
                    self.horizontal_layout_enabled = data.get("horizontal_layout_enabled", self.horizontal_layout_enabled) # Load new setting
            except Exception:
                pass

    def save_settings(self):
        CONFIG_PATH = os.path.join(os.path.dirname(__file__), "weather_widget_config.json")
        data = {
            "text_color": list(self.text_color),
            "temp_spacing": self.temp_spacing,
            "cond_spacing": self.cond_spacing,
            "loc_spacing": self.loc_spacing,
            "widget_width": self.widget_width,
            "widget_height": self.widget_height,
            "horizontal_layout_enabled": self.horizontal_layout_enabled # Save new setting
        }
        try:
            with open(CONFIG_PATH, "w") as f:
                json.dump(data, f)
        except Exception:
            pass

    def init_ui(self):
        self.setWindowFlags(
            QtCore.Qt.FramelessWindowHint
            | QtCore.Qt.Tool
        )
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.resize(self.widget_width, self.widget_height)

        # Clear existing layout if re-initializing
        if self.layout() is not None:
            QtWidgets.QWidget().setLayout(self.layout()) # Detach and delete the old layout

        font_path = os.path.join(os.path.dirname(__file__), "Anurati-Regular.otf") # Use relative path directly for now
        if os.path.exists(font_path):
            font_id = QtGui.QFontDatabase.addApplicationFont(font_path)
            families = QtGui.QFontDatabase.applicationFontFamilies(font_id)
            self.font_family = families[0] if families else "Segoe UI"
        else:
            self.font_family = "Segoe UI"

        # Labels (created once)
        self.temp_label = QtWidgets.QLabel("N/A°C")
        self.icon_label = QtWidgets.QLabel("?")
        self.condition_label = QtWidgets.QLabel("Loading...")
        self.location_label = QtWidgets.QLabel("Fetching Location...")

        # Alignment settings (can be adjusted per layout if needed in update_styles)
        self.temp_label.setAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
        self.icon_label.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        # self.condition_label.setAlignment(QtCore.Qt.AlignCenter)
        # self.location_label.setAlignment(QtCore.Qt.AlignCenter)

        if self.horizontal_layout_enabled:
            # New Horizontal Layout Design
            self.main_layout = QtWidgets.QVBoxLayout(self) # Use QVBoxLayout as the main layout
            self.main_layout.setContentsMargins(10, 10, 10, 10)
            self.main_layout.setSpacing(10) # Adjusted spacing

            # Top row: Temperature and Icon (side-by-side, centered horizontally)
            temp_icon_layout = QtWidgets.QHBoxLayout()
            temp_icon_layout.setContentsMargins(0, 0, 0, 0)
            temp_icon_layout.setSpacing(10)
            temp_icon_layout.addStretch() # Add stretch to center content
            temp_icon_layout.addWidget(self.temp_label)
            temp_icon_layout.addWidget(self.icon_label)
            temp_icon_layout.addStretch() # Add stretch to center content

            self.main_layout.addLayout(temp_icon_layout)

            # Bottom row: Condition and Location (stacked vertically, centered horizontally)
            condition_location_layout = QtWidgets.QVBoxLayout()
            condition_location_layout.setContentsMargins(0, 0, 0, 0)
            condition_location_layout.setSpacing(4)
            condition_location_layout.addWidget(self.condition_label, alignment=QtCore.Qt.AlignCenter) # Center condition label
            condition_location_layout.addWidget(self.location_label, alignment=QtCore.Qt.AlignCenter) # Center location label
            condition_location_layout.addStretch() # Push content up within this vertical layout

            self.main_layout.addLayout(condition_location_layout)

            self.main_layout.addStretch() # Add stretch to push everything to the top overall

        else:
            # Original Vertical Layout
            self.main_layout = QtWidgets.QVBoxLayout(self)
            self.main_layout.setContentsMargins(0, 0, 0, 0)
            self.main_layout.setSpacing(4)
            self.main_layout.addStretch()

            # Layout for Temperature and Icon (centered in vertical layout)
            temp_icon_layout = QtWidgets.QHBoxLayout()
            temp_icon_layout.setContentsMargins(0, 0, 0, 0)
            temp_icon_layout.setSpacing(10)
            temp_icon_layout.addStretch()
            temp_icon_layout.addWidget(self.temp_label)
            temp_icon_layout.addWidget(self.icon_label)
            temp_icon_layout.addStretch()

            self.main_layout.addLayout(temp_icon_layout)

            # Condition Label
            self.condition_label.setAlignment(QtCore.Qt.AlignCenter) # Ensure original alignment is set
            self.main_layout.addWidget(self.condition_label)

            # Location Label
            self.location_label.setAlignment(QtCore.Qt.AlignCenter) # Ensure original alignment is set
            self.main_layout.addWidget(self.location_label)

            self.main_layout.addStretch()

        # Placeholder timer - won't update automatically without weather data logic
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.update_display) # Uncomment and implement weather fetching here
        self.timer.start(600000) # Update every 10 minutes (example)

        self.update_display()
        self.update_styles() # Ensure styles are applied after layout is set
        self.show()

        self.dragging = False
        self.drag_offset = QtCore.QPoint()

    def fetch_weather(self, city, api_key):
        try:
            url = self.WEATHER_API_URL.format(city=city, api_key=api_key)
            response = requests.get(url)
            response.raise_for_status() # Raise an exception for bad status codes
            data = response.json()

            temperature = data['main']['temp']
            condition = data['weather'][0]['description']
            icon_code = data['weather'][0]['icon']
            city_name = data['name']

            return {
                'temperature': temperature,
                'condition': condition,
                'icon_code': icon_code,
                'city_name': city_name
            }
        except requests.exceptions.RequestException as e:
            print(f"Error fetching weather data: {e}")
            return None
        except KeyError as e:
            print(f"Error parsing weather data: Missing key {e}")
            return None

    def update_display(self):
        # This will be updated with actual weather data later
        weather_data = self.fetch_weather(self.DEFAULT_CITY, self.API_KEY)

        if weather_data:
            self.temp_label.setText(f"{weather_data['temperature']:.1f}°C")
            # Basic icon representation (you can expand this)
            icon = "?"
            if "clear" in weather_data['condition']:
                icon = "☀️"
            elif "cloud" in weather_data['condition']:
                icon = "☁️"
            elif "rain" in weather_data['condition']:
                icon = "🌧️"
            elif "snow" in weather_data['condition']:
                icon = "❄️"
            elif "thunder" in weather_data['condition']:
                icon = "⛈️"

            self.icon_label.setText(icon)
            self.condition_label.setText(weather_data['condition'].capitalize())
            self.location_label.setText(weather_data['city_name'])
        else:
            self.temp_label.setText("N/A")
            self.icon_label.setText("!")
            self.condition_label.setText("Could not fetch weather")
            self.location_label.setText("Check API key/city")

    def update_styles(self):
        # Apply styles similar to the clock widget

        # Temperature Label
        temp_font = QtGui.QFont(self.font_family)
        # Adjust font size based on layout
        temp_font.setPointSize(48 if not self.horizontal_layout_enabled else 60) # Larger in horizontal
        temp_font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, self.temp_spacing)
        self.temp_label.setFont(temp_font)
        r, g, b, a = self.text_color
        self.temp_label.setStyleSheet(f"""
            QLabel {{
                color: rgba(255, 255, 255, {a});
                background: transparent;
            }}
        """)

        # Icon Label
        icon_font = QtGui.QFont(self.font_family)
        # Adjust font size based on layout
        icon_font.setPointSize(36 if not self.horizontal_layout_enabled else 48) # Larger in horizontal
        # icon_font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, self.temp_spacing) # Maybe different spacing for icon?
        self.icon_label.setFont(icon_font)
        self.icon_label.setStyleSheet(f"""
            QLabel {{
                color: rgba(255, 255, 255, {int(a * 0.9)});
                background: transparent;
            }}
        """)

        # Condition Label
        condition_font = QtGui.QFont(self.font_family)
        # Adjust font size based on layout
        condition_font.setPointSize(16 if not self.horizontal_layout_enabled else 18) # Slightly larger in horizontal
        condition_font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, self.cond_spacing)
        self.condition_label.setFont(condition_font)
        self.condition_label.setStyleSheet(f"""
            QLabel {{
                color: rgba(255, 255, 255, 255);
                background: transparent;
            }}
        """)

        # Location Label
        location_font = QtGui.QFont(self.font_family)
        # Adjust font size based on layout
        location_font.setPointSize(14 if not self.horizontal_layout_enabled else 16) # Slightly larger in horizontal
        location_font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, self.loc_spacing)
        self.location_label.setFont(location_font)
        self.location_label.setStyleSheet(f"""
            QLabel {{
                color: rgba(255, 255, 255, 255);
                background: transparent;
            }}
        """)

        self.resize(self.widget_width, self.widget_height)

    def contextMenuEvent(self, event):
        menu = QtWidgets.QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background: rgba(255, 255, 255, 240);
                border: 1px solid rgba(0, 0, 0, 50);
                border-radius: 4px;
                padding: 4px;
            }
            QMenu::item {
                color: rgba(0, 0, 0, 230);
                padding: 4px 20px;
                font-size: 12px;
            }
            QMenu::item:selected {
                background: rgba(0, 0, 0, 20);
            }
        """)

        settings_action = menu.addAction("Settings")
        settings_action.triggered.connect(self.open_settings)

        menu.addSeparator()

        close_action = menu.addAction("Close")
        close_action.triggered.connect(QtWidgets.QApplication.instance().quit)

        menu.exec(event.globalPos())

    def open_settings(self):
        dlg = WeatherSettingsDialog(self)
        dlg.exec()

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.dragging = True
            self.drag_offset = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        if self.dragging and event.buttons() == QtCore.Qt.LeftButton:
            new_pos = event.globalPosition().toPoint() - self.drag_offset
            self.move(new_pos)
            event.accept()

    def mouseReleaseEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.dragging = False
            event.accept()

if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    widget = WeatherWidget()
    sys.exit(app.exec()) 