#!/usr/bin/env python3
"""
Premium Power Connection Notification System
A visually stunning, modern notification that appears when laptop power is connected.
"""

from PySide6 import QtWidgets, QtCore, QtGui
import sys
import os
from premium_power_notification_widget import PremiumPowerNotificationWidget


class PremiumPowerNotificationApp(QtWidgets.QApplication):
    """Premium application for the power notification system."""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Set application properties
        self.setApplicationName("Premium Power Notification")
        self.setApplicationVersion("2.0.0")
        self.setOrganizationName("Wigfy Premium")
        
        # Create premium notification widget
        self.notification_widget = PremiumPowerNotificationWidget()
        
        # Create premium system tray
        self.create_premium_system_tray()
        
        # Start power monitoring
        self.notification_widget.start_monitoring()
        
        print("🔋 Premium Power Notification System initialized!")
        
    def create_premium_system_tray(self):
        """Create premium system tray icon with enhanced features."""
        if not QtWidgets.QSystemTrayIcon.isSystemTrayAvailable():
            print("System tray is not available on this system.")
            return
            
        # Create tray icon
        self.tray_icon = QtWidgets.QSystemTrayIcon(self)
        
        # Set premium icon
        icon_path = os.path.join(os.path.dirname(__file__), "images", "battery-bolt.svg")
        if os.path.exists(icon_path):
            self.tray_icon.setIcon(QtGui.QIcon(icon_path))
        else:
            # Use premium default icon
            style = self.style()
            icon = style.standardIcon(QtWidgets.QStyle.StandardPixmap.SP_ComputerIcon)
            self.tray_icon.setIcon(icon)
            
        # Create premium context menu
        tray_menu = QtWidgets.QMenu()
        tray_menu.setStyleSheet("""
            QMenu {
                background-color: rgba(28, 28, 30, 0.95);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        
        # Test notification action
        test_action = QtGui.QAction("✨ Show Test Notification", self)
        test_action.triggered.connect(self.show_test_notification)
        tray_menu.addAction(test_action)
        
        tray_menu.addSeparator()
        
        # Battery status action
        status_action = QtGui.QAction("🔋 Battery Status", self)
        status_action.triggered.connect(self.show_premium_battery_status)
        tray_menu.addAction(status_action)
        
        # Theme toggle action
        theme_action = QtGui.QAction("🎨 Toggle Theme", self)
        theme_action.triggered.connect(self.toggle_theme)
        tray_menu.addAction(theme_action)
        
        tray_menu.addSeparator()
        
        # Settings action
        settings_action = QtGui.QAction("⚙️ Settings", self)
        settings_action.triggered.connect(self.show_settings)
        tray_menu.addAction(settings_action)
        
        tray_menu.addSeparator()
        
        # Quit action
        quit_action = QtGui.QAction("❌ Quit", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.setToolTip("Premium Power Notification System")
        
        # Show tray icon
        self.tray_icon.show()
        
        # Connect tray icon activation
        self.tray_icon.activated.connect(self.on_tray_activated)
        
        # Show welcome notification
        self.tray_icon.showMessage(
            "Premium Power Notification",
            "System is now monitoring power connections",
            QtWidgets.QSystemTrayIcon.MessageIcon.Information,
            3000
        )
        
    def on_tray_activated(self, reason):
        """Handle tray icon activation."""
        if reason == QtWidgets.QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_test_notification()
            
    def show_test_notification(self):
        """Show a premium test notification."""
        self.notification_widget.trigger_test_notification()
        
    def show_premium_battery_status(self):
        """Show premium battery status dialog."""
        battery_info = self.notification_widget.power_monitor.get_current_battery_info()
        
        # Create premium message box
        msg_box = QtWidgets.QMessageBox()
        msg_box.setWindowTitle("Battery Status")
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: rgba(28, 28, 30, 0.95);
                color: white;
                border-radius: 12px;
            }
            QMessageBox QPushButton {
                background-color: #007AFF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
            }
            QMessageBox QPushButton:hover {
                background-color: #0056CC;
            }
        """)
        
        if battery_info:
            status = "🔌 Connected" if battery_info['power_plugged'] else "🔋 Disconnected"
            level_emoji = self.get_battery_emoji(battery_info['percent'])
            
            message = f"""
            <h3 style='color: #34C759; margin-bottom: 16px;'>Battery Information</h3>
            <p><strong>Level:</strong> {level_emoji} {battery_info['percent']}%</p>
            <p><strong>Power:</strong> {status}</p>
            <p><strong>Category:</strong> {battery_info['battery_level'].title()}</p>
            <p><strong>Charging Speed:</strong> {battery_info['charging_speed']}</p>
            """
        else:
            message = """
            <h3 style='color: #FF9500;'>No Battery Information</h3>
            <p>Battery information not available (desktop system?)</p>
            """
            
        msg_box.setText(message)
        msg_box.exec()
        
    def get_battery_emoji(self, percent):
        """Get appropriate battery emoji based on percentage."""
        if percent >= 90:
            return "🔋"
        elif percent >= 60:
            return "🔋"
        elif percent >= 30:
            return "🪫"
        else:
            return "🪫"
            
    def toggle_theme(self):
        """Toggle between light and dark themes."""
        current_theme = self.notification_widget.current_theme
        new_theme = "light_theme" if current_theme == "dark_theme" else "dark_theme"
        self.notification_widget.current_theme = new_theme
        self.notification_widget.apply_premium_styles()
        
        self.tray_icon.showMessage(
            "Theme Changed",
            f"Switched to {'Light' if new_theme == 'light_theme' else 'Dark'} theme",
            QtWidgets.QSystemTrayIcon.MessageIcon.Information,
            2000
        )
        
    def show_settings(self):
        """Show premium settings dialog."""
        settings_dialog = PremiumSettingsDialog(self.notification_widget)
        settings_dialog.exec()
        
    def quit_application(self):
        """Quit the application cleanly."""
        self.notification_widget.stop_monitoring()
        self.quit()


class PremiumSettingsDialog(QtWidgets.QDialog):
    """Premium settings dialog for customization."""
    
    def __init__(self, notification_widget):
        super().__init__()
        self.notification_widget = notification_widget
        self.init_ui()
        
    def init_ui(self):
        """Initialize premium settings UI."""
        self.setWindowTitle("Premium Settings")
        self.setFixedSize(400, 300)
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(28, 28, 30, 0.98);
                color: white;
                border-radius: 12px;
            }
            QLabel {
                color: white;
                font-size: 14px;
            }
            QPushButton {
                background-color: #007AFF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #0056CC;
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #007AFF;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(self)
        
        # Title
        title = QtWidgets.QLabel("⚙️ Premium Settings")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Test button
        test_btn = QtWidgets.QPushButton("✨ Test Notification")
        test_btn.clicked.connect(self.notification_widget.trigger_test_notification)
        layout.addWidget(test_btn)
        
        layout.addStretch()
        
        # Close button
        close_btn = QtWidgets.QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)


def main():
    """Main entry point for the premium system."""
    # Create application
    app = PremiumPowerNotificationApp(sys.argv)
    
    # Show startup message
    print("🚀 Premium Power Notification System started!")
    print("✨ Features: Glass morphism, advanced animations, smart content")
    print("🎯 Right-click system tray icon for options")
    print("⚡ Double-click tray icon to test notification")
    print("🛑 Press Ctrl+C to quit")
    
    # Run application
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n👋 Shutting down premium system...")
        app.quit_application()


if __name__ == "__main__":
    main()
