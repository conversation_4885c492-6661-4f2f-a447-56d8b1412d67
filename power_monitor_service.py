from PySide6 import QtCore
import psutil


class PowerMonitorService(QtCore.QObject):
    """Service for monitoring power connection changes."""
    
    power_connected = QtCore.Signal()
    power_disconnected = QtCore.Signal()
    
    def __init__(self, polling_interval=1000):
        super().__init__()
        self.polling_interval = polling_interval
        self.previous_power_state = None
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.check_power_status)
        
    def start_monitoring(self):
        """Start monitoring power status changes."""
        # Get initial state
        try:
            battery = psutil.sensors_battery()
            if battery:
                self.previous_power_state = battery.power_plugged
            else:
                self.previous_power_state = None
        except Exception as e:
            print(f"Error getting initial battery status: {e}")
            self.previous_power_state = None
            
        self.timer.start(self.polling_interval)
        print("Power monitoring started")
        
    def stop_monitoring(self):
        """Stop monitoring power status changes."""
        self.timer.stop()
        print("Power monitoring stopped")
        
    def check_power_status(self):
        """Check current power status and emit signals on changes."""
        try:
            battery = psutil.sensors_battery()
            if battery:
                current_power_state = battery.power_plugged
                
                # Check for state change
                if self.previous_power_state is not None:
                    if not self.previous_power_state and current_power_state:
                        # Power was disconnected, now connected
                        print("Power connected detected!")
                        self.power_connected.emit()
                    elif self.previous_power_state and not current_power_state:
                        # Power was connected, now disconnected
                        print("Power disconnected detected!")
                        self.power_disconnected.emit()
                
                self.previous_power_state = current_power_state
            else:
                # No battery found (desktop system)
                if self.previous_power_state is not None:
                    self.previous_power_state = None
                    
        except Exception as e:
            print(f"Error checking power status: {e}")
            
    def get_current_battery_info(self):
        """Get detailed current battery information."""
        try:
            battery = psutil.sensors_battery()
            if battery:
                percent = int(battery.percent)
                power_plugged = battery.power_plugged
                secsleft = getattr(battery, 'secsleft', None)

                # Determine charging speed category (simplified)
                charging_speed = "Standard"
                if power_plugged:
                    if percent < 20:
                        charging_speed = "Fast"
                    elif percent > 80:
                        charging_speed = "Trickle"

                # Determine battery status message
                status_message = self.get_status_message(percent, power_plugged)

                return {
                    'percent': percent,
                    'power_plugged': power_plugged,
                    'secsleft': secsleft,
                    'charging_speed': charging_speed,
                    'status_message': status_message,
                    'battery_level': self.get_battery_level_category(percent)
                }
            return None
        except Exception as e:
            print(f"Error getting battery info: {e}")
            return None

    def get_battery_level_category(self, percent):
        """Categorize battery level."""
        if percent >= 80:
            return "high"
        elif percent >= 50:
            return "medium"
        elif percent >= 20:
            return "low"
        else:
            return "critical"

    def get_status_message(self, percent, power_plugged):
        """Get contextual status message based on battery state."""
        if not power_plugged:
            return f"Battery: {percent}%"

        if percent >= 95:
            return "Power Connected • Battery Full"
        elif percent >= 80:
            return f"Power Connected • {percent}%"
        elif percent <= 20:
            return "Power Connected • Charging..."
        else:
            return f"Power Connected • Charging {percent}%"
