from PySide6 import QtCore
import psutil


class PowerMonitorService(QtCore.QObject):
    """Service for monitoring power connection changes."""
    
    power_connected = QtCore.Signal()
    power_disconnected = QtCore.Signal()
    
    def __init__(self, polling_interval=1000):
        super().__init__()
        self.polling_interval = polling_interval
        self.previous_power_state = None
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.check_power_status)
        
    def start_monitoring(self):
        """Start monitoring power status changes."""
        # Get initial state
        try:
            battery = psutil.sensors_battery()
            if battery:
                self.previous_power_state = battery.power_plugged
            else:
                self.previous_power_state = None
        except Exception as e:
            print(f"Error getting initial battery status: {e}")
            self.previous_power_state = None
            
        self.timer.start(self.polling_interval)
        print("Power monitoring started")
        
    def stop_monitoring(self):
        """Stop monitoring power status changes."""
        self.timer.stop()
        print("Power monitoring stopped")
        
    def check_power_status(self):
        """Check current power status and emit signals on changes."""
        try:
            battery = psutil.sensors_battery()
            if battery:
                current_power_state = battery.power_plugged
                
                # Check for state change
                if self.previous_power_state is not None:
                    if not self.previous_power_state and current_power_state:
                        # Power was disconnected, now connected
                        print("Power connected detected!")
                        self.power_connected.emit()
                    elif self.previous_power_state and not current_power_state:
                        # Power was connected, now disconnected
                        print("Power disconnected detected!")
                        self.power_disconnected.emit()
                
                self.previous_power_state = current_power_state
            else:
                # No battery found (desktop system)
                if self.previous_power_state is not None:
                    self.previous_power_state = None
                    
        except Exception as e:
            print(f"Error checking power status: {e}")
            
    def get_current_battery_info(self):
        """Get current battery information."""
        try:
            battery = psutil.sensors_battery()
            if battery:
                return {
                    'percent': battery.percent,
                    'power_plugged': battery.power_plugged,
                    'secsleft': getattr(battery, 'secsleft', None)
                }
            return None
        except Exception as e:
            print(f"Error getting battery info: {e}")
            return None
